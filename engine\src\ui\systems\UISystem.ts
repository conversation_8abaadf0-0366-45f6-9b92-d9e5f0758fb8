/**
 * UISystem.ts
 *
 * UI系统，管理所有UI元素
 */

import { System } from '../../core/System';
import { World } from '../../core/World';
import { Entity } from '../../core/Entity';
import { Vector2 } from 'three';
import { UIComponent } from '../components/UIComponent';
import { UI2DComponent } from '../components/UI2DComponent';
import { UI3DComponent } from '../components/UI3DComponent';
import { UIAnimationComponent } from '../components/UIAnimationComponent';
import { UILayoutComponent } from '../components/UILayoutComponent';
import { UIEventComponent } from '../components/UIEventComponent';

/**
 * UI系统配置
 */
export interface UISystemConfig {
  // 是否启用调试模式
  debug?: boolean;

  // 是否自动创建HTML容器
  autoCreateContainer?: boolean;

  // HTML容器ID
  containerId?: string;

  // 是否启用事件系统
  enableEvents?: boolean;

  // 是否启用布局系统
  enableLayouts?: boolean;

  // 是否启用动画系统
  enableAnimations?: boolean;
}

/**
 * UI系统
 * 管理所有UI元素
 */
export class UISystem extends System {
  // 世界引用（已移除，通过引擎获取）

  // UI元素列表
  private uiComponents: Map<Entity, UIComponent> = new Map();

  // 2D UI元素列表
  private ui2DComponents: Map<Entity, UI2DComponent> = new Map();

  // 3D UI元素列表
  private ui3DComponents: Map<Entity, UI3DComponent> = new Map();

  // 动画组件列表
  private uiAnimationComponents: Map<Entity, UIAnimationComponent> = new Map();

  // 布局组件列表
  private uiLayoutComponents: Map<Entity, UILayoutComponent> = new Map();

  // 事件组件列表
  private uiEventComponents: Map<Entity, UIEventComponent> = new Map();

  // HTML容器元素
  private container?: HTMLElement;

  // 配置
  private config: UISystemConfig;

  // 鼠标位置
  private mousePosition: Vector2 = new Vector2();

  // 按键状态
  private keyStates: Map<string, boolean> = new Map();

  // 修饰键状态
  private modifiers = {
    altKey: false,
    ctrlKey: false,
    shiftKey: false,
    metaKey: false
  };

  /**
   * 构造函数
   * @param world 世界实例
   * @param config UI系统配置
   */
  constructor(config: UISystemConfig = {}) {
    // 调用基类构造函数，传入优先级
    super(600);

    this.config = {
      debug: config.debug || false,
      autoCreateContainer: config.autoCreateContainer !== undefined ? config.autoCreateContainer : true,
      containerId: config.containerId || 'ui-container',
      enableEvents: config.enableEvents !== undefined ? config.enableEvents : true,
      enableLayouts: config.enableLayouts !== undefined ? config.enableLayouts : true,
      enableAnimations: config.enableAnimations !== undefined ? config.enableAnimations : true
    };

    // 如果自动创建容器
    if (this.config.autoCreateContainer) {
      this.createContainer();
    }

    // 如果启用事件系统
    if (this.config.enableEvents) {
      this.setupEventListeners();
    }
  }

  /**
   * 创建HTML容器
   */
  private createContainer(): void {
    // 检查容器是否已存在
    let container = document.getElementById(this.config.containerId!);

    // 如果不存在，则创建
    if (!container) {
      container = document.createElement('div');
      container.id = this.config.containerId!;
      container.style.position = 'absolute';
      container.style.top = '0';
      container.style.left = '0';
      container.style.width = '100%';
      container.style.height = '100%';
      container.style.pointerEvents = 'none';
      container.style.overflow = 'hidden';
      container.style.zIndex = '1000';

      document.body.appendChild(container);
    }

    this.container = container;
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 鼠标移动事件
    window.addEventListener('mousemove', (event) => {
      this.mousePosition.set(event.clientX, event.clientY);
      this.handleMouseMove(event);
    });

    // 鼠标按下事件
    window.addEventListener('mousedown', (event) => {
      this.handleMouseDown(event);
    });

    // 鼠标释放事件
    window.addEventListener('mouseup', (event) => {
      this.handleMouseUp(event);
    });

    // 键盘按下事件
    window.addEventListener('keydown', (event) => {
      this.keyStates.set(event.key, true);
      this.updateModifiers(event);
      this.handleKeyDown(event);
    });

    // 键盘释放事件
    window.addEventListener('keyup', (event) => {
      this.keyStates.set(event.key, false);
      this.updateModifiers(event);
      this.handleKeyUp(event);
    });

    // 窗口大小改变事件
    window.addEventListener('resize', () => {
      this.handleResize();
    });
  }

  /**
   * 更新修饰键状态
   * @param event 键盘事件
   */
  private updateModifiers(event: KeyboardEvent): void {
    this.modifiers.altKey = event.altKey;
    this.modifiers.ctrlKey = event.ctrlKey;
    this.modifiers.shiftKey = event.shiftKey;
    this.modifiers.metaKey = event.metaKey;
  }

  /**
   * 处理鼠标移动事件
   * @param event 鼠标事件
   */
  private handleMouseMove(event: MouseEvent): void {
    // 遍历所有事件组件
    for (const [_entity, eventComponent] of this.uiEventComponents) {
      // 相机暂时设为undefined，实际项目中可能需要从场景或相机管理器获取
      const camera = undefined;

      // 获取所有UI元素
      const uiElements = Array.from(this.uiComponents.values());

      // 处理鼠标移动
      eventComponent.handleMouseMove(
        event.clientX,
        event.clientY,
        uiElements,
        camera
      );
    }
  }

  /**
   * 处理鼠标按下事件
   * @param event 鼠标事件
   */
  private handleMouseDown(event: MouseEvent): void {
    // 遍历所有事件组件
    for (const [_entity, eventComponent] of this.uiEventComponents) {
      // 相机暂时设为undefined，实际项目中可能需要从场景或相机管理器获取
      const camera = undefined;

      // 获取所有UI元素
      const uiElements = Array.from(this.uiComponents.values());

      // 处理鼠标按下
      eventComponent.handleMouseDown(
        event.clientX,
        event.clientY,
        event.button,
        uiElements,
        camera
      );
    }
  }

  /**
   * 处理鼠标释放事件
   * @param event 鼠标事件
   */
  private handleMouseUp(event: MouseEvent): void {
    // 遍历所有事件组件
    for (const [_entity, eventComponent] of this.uiEventComponents) {
      // 相机暂时设为undefined，实际项目中可能需要从场景或相机管理器获取
      const camera = undefined;

      // 获取所有UI元素
      const uiElements = Array.from(this.uiComponents.values());

      // 处理鼠标释放
      eventComponent.handleMouseUp(
        event.clientX,
        event.clientY,
        event.button,
        uiElements,
        camera
      );
    }
  }

  /**
   * 处理键盘按下事件
   * @param event 键盘事件
   */
  private handleKeyDown(event: KeyboardEvent): void {
    // 遍历所有事件组件
    for (const [_entity, eventComponent] of this.uiEventComponents) {
      // 处理键盘按下
      eventComponent.handleKeyDown(
        event.key,
        event.key.charCodeAt(0), // 使用字符编码作为 keyCode 的替代
        this.modifiers
      );
    }
  }

  /**
   * 处理键盘释放事件
   * @param event 键盘事件
   */
  private handleKeyUp(event: KeyboardEvent): void {
    // 遍历所有事件组件
    for (const [_entity, eventComponent] of this.uiEventComponents) {
      // 处理键盘释放
      eventComponent.handleKeyUp(
        event.key,
        event.key.charCodeAt(0), // 使用字符编码作为 keyCode 的替代
        this.modifiers
      );
    }
  }

  /**
   * 处理窗口大小改变事件
   */
  private handleResize(): void {
    // 更新所有2D UI元素
    for (const [_entity, component] of this.ui2DComponents) {
      // 如果组件是响应式的，则更新
      if ((component as any).responsive) {
        component.update(0);
      }
    }
  }

  /**
   * 注册UI组件
   * @param entity 实体
   * @param component UI组件
   */
  registerUIComponent(entity: Entity, component: UIComponent): void {
    this.uiComponents.set(entity, component);

    // 根据组件类型添加到相应的列表
    if (component instanceof UI2DComponent) {
      this.ui2DComponents.set(entity, component);

      // 如果有容器，将HTML元素添加到容器
      if (this.container && component.htmlElement && !component.htmlElement.parentElement) {
        this.container.appendChild(component.htmlElement);
      }
    } else if (component instanceof UI3DComponent) {
      this.ui3DComponents.set(entity, component);
    }
  }

  /**
   * 注册UI动画组件
   * @param entity 实体
   * @param component UI动画组件
   */
  registerUIAnimationComponent(entity: Entity, component: UIAnimationComponent): void {
    this.uiAnimationComponents.set(entity, component);
  }

  /**
   * 注册UI布局组件
   * @param entity 实体
   * @param component UI布局组件
   */
  registerUILayoutComponent(entity: Entity, component: UILayoutComponent): void {
    this.uiLayoutComponents.set(entity, component);
  }

  /**
   * 注册UI事件组件
   * @param entity 实体
   * @param component UI事件组件
   */
  registerUIEventComponent(entity: Entity, component: UIEventComponent): void {
    this.uiEventComponents.set(entity, component);
  }

  /**
   * 注销UI组件
   * @param entity 实体
   */
  unregisterUIComponent(entity: Entity): void {
    const component = this.uiComponents.get(entity);
    if (component) {
      // 从列表中移除
      this.uiComponents.delete(entity);

      // 根据组件类型从相应的列表中移除
      if (component instanceof UI2DComponent) {
        this.ui2DComponents.delete(entity);

        // 如果有HTML元素，从DOM中移除
        if (component.htmlElement && component.htmlElement.parentElement) {
          component.htmlElement.parentElement.removeChild(component.htmlElement);
        }
      } else if (component instanceof UI3DComponent) {
        this.ui3DComponents.delete(entity);
      }

      // 销毁组件
      component.dispose();
    }
  }

  /**
   * 注销UI动画组件
   * @param entity 实体
   */
  unregisterUIAnimationComponent(entity: Entity): void {
    this.uiAnimationComponents.delete(entity);
  }

  /**
   * 注销UI布局组件
   * @param entity 实体
   */
  unregisterUILayoutComponent(entity: Entity): void {
    this.uiLayoutComponents.delete(entity);
  }

  /**
   * 注销UI事件组件
   * @param entity 实体
   */
  unregisterUIEventComponent(entity: Entity): void {
    this.uiEventComponents.delete(entity);
  }

  /**
   * 更新系统
   * @param deltaTime 时间增量
   */
  update(deltaTime: number): void {
    // 更新所有UI组件
    for (const [_entity, component] of this.uiComponents) {
      component.update(deltaTime);
    }

    // 如果启用动画系统，更新所有动画组件
    if (this.config.enableAnimations) {
      for (const [_entity, component] of this.uiAnimationComponents) {
        component.update(deltaTime);
      }
    }

    // 如果启用布局系统，应用所有布局
    if (this.config.enableLayouts) {
      for (const [entity, layoutComponent] of this.uiLayoutComponents) {
        const uiComponent = this.uiComponents.get(entity);
        if (uiComponent) {
          layoutComponent.applyLayout(uiComponent);
        }
      }
    }

    // 更新所有3D UI元素
    // 相机暂时设为undefined，实际项目中可能需要从场景或相机管理器获取
    const camera = undefined;
    for (const [_entity, component] of this.ui3DComponents) {
      (component as UI3DComponent).update(deltaTime, camera);
    }
  }

  /**
   * 渲染系统
   */
  render(): void {
    // 渲染所有UI组件
    for (const [_entity, component] of this.uiComponents) {
      component.render();
    }
  }

  /**
   * 销毁系统
   */
  dispose(): void {
    // 移除事件监听器
    if (this.config.enableEvents) {
      window.removeEventListener('mousemove', this.handleMouseMove as any);
      window.removeEventListener('mousedown', this.handleMouseDown as any);
      window.removeEventListener('mouseup', this.handleMouseUp as any);
      window.removeEventListener('keydown', this.handleKeyDown as any);
      window.removeEventListener('keyup', this.handleKeyUp as any);
      window.removeEventListener('resize', this.handleResize as any);
    }

    // 销毁所有UI组件
    for (const [_entity, component] of this.uiComponents) {
      component.dispose();
    }

    // 清空所有列表
    this.uiComponents.clear();
    this.ui2DComponents.clear();
    this.ui3DComponents.clear();
    this.uiAnimationComponents.clear();
    this.uiLayoutComponents.clear();
    this.uiEventComponents.clear();

    // 移除容器
    if (this.container && this.container.parentElement) {
      this.container.parentElement.removeChild(this.container);
    }

    this.container = undefined;
  }
}
