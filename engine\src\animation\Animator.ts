/**
 * 动画控制器
 * 用于控制和混合多个动画片段的播放
 */
import * as THREE from 'three';
import { Component } from '../core/Component';
import { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';
import { AnimationClip, LoopMode } from './AnimationClip';
import { AnimationClipAdapter } from './utils/AnimationClipAdapter';

/**
 * 动画状态
 */
export enum AnimationState {
  /** 停止 */
  STOPPED = 'stopped',
  /** 播放中 */
  PLAYING = 'playing',
  /** 暂停 */
  PAUSED = 'paused',
  /** 混合中 */
  BLENDING = 'blending'
}

/**
 * 动画事件类型
 */
export enum AnimationEventType {
  /** 开始 */
  START = 'start',
  /** 停止 */
  STOP = 'stop',
  /** 暂停 */
  PAUSE = 'pause',
  /** 恢复 */
  RESUME = 'resume',
  /** 循环 */
  LOOP = 'loop',
  /** 完成 */
  COMPLETE = 'complete',
  /** 混合开始 */
  BLEND_START = 'blendStart',
  /** 混合完成 */
  BLEND_COMPLETE = 'blendComplete'
}

/**
 * 动画控制器选项
 */
export interface AnimatorOptions {
  /** 目标实体 */
  entity?: Entity;
  /** 动画片段 */
  clips?: AnimationClip[];
  /** 是否自动播放 */
  autoPlay?: boolean;
  /** 默认混合时间（秒） */
  defaultBlendTime?: number;
  /** 时间缩放 */
  timeScale?: number;
}

/**
 * 动画控制器组件
 */
export class Animator extends Component {
  /** 组件类型 */
  public static readonly type: string = 'Animator';

  /** 目标实体引用 */
  protected _targetEntity: Entity | null = null;

  /** 动画片段映射 */
  private clips: Map<string, AnimationClip> = new Map();

  /** 当前播放的动画片段 */
  private currentClip: AnimationClip | null = null;

  /** 下一个要播放的动画片段（用于混合） */
  private nextClip: AnimationClip | null = null;

  /** 当前动画状态 */
  private state: AnimationState = AnimationState.STOPPED;

  /** 当前播放时间（秒） */
  private time: number = 0;

  /** 混合开始时间（秒） */
  private blendStartTime: number = 0;

  /** 混合持续时间（秒） */
  private blendTime: number = 0;

  /** 混合因子（0-1） */
  private blendFactor: number = 0;

  /** 默认混合时间（秒） */
  private defaultBlendTime: number = 0.3;

  /** 时间缩放 */
  private timeScale: number = 1.0;

  /** 是否循环 */
  private loop: boolean = true;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** Three.js动画混合器（用于兼容） */
  private mixer: THREE.AnimationMixer | null = null;

  /** Three.js动画动作映射 */
  private actions: Map<string, THREE.AnimationAction> = new Map();

  /** 当前动画状态 */
  private animationState: Map<string, any> = new Map();

  /** 缓存的动画状态 */
  private _cachedAnimationState: Map<string, any> | null = null;

  /** 缓存的时间 */
  private _cachedTime: number = -1;

  /** 缓存的混合因子 */
  private _cachedBlendFactor: number = -1;

  /** 是否需要更新 */
  private _needsUpdate: boolean = true;

  /** 是否启用缓存 */
  private _cacheEnabled: boolean = true;

  /** 动画参数映射 */
  private parameters: Map<string, any> = new Map();

  /**
   * 创建动画控制器
   * @param options 动画控制器选项
   */
  constructor(options: AnimatorOptions = {}) {
    super(Animator.type);

    if (options.entity) {
      this.setEntity(options.entity);
    }

    if (options.clips) {
      for (const clip of options.clips) {
        this.addClip(clip);
      }
    }

    this.defaultBlendTime = options.defaultBlendTime || 0.3;
    this.timeScale = options.timeScale || 1.0;

    if (options.autoPlay && this.clips.size > 0) {
      const firstClipResult = this.clips.values().next();
      if (!firstClipResult.done && firstClipResult.value) {
        const firstClip = firstClipResult.value;
        this.play(firstClip.name);
      }
    }
  }

  /**
   * 设置目标实体
   * @param entity 实体
   */
  public setEntity(entity: Entity): void {
    this._targetEntity = entity;
    super.setEntity(entity);

    // 如果实体有Three.js对象，则创建混合器
    const object = entity.getComponent('Transform');
    if (object) {
      // 使用类型断言访问getObject3D方法
      const transformWithObject3D = object as any;
      if (typeof transformWithObject3D.getObject3D === 'function') {
        const object3D = transformWithObject3D.getObject3D();
        if (object3D) {
          this.mixer = new THREE.AnimationMixer(object3D);

          // 重新创建所有动作
          this.actions.clear();
          for (const [name, clip] of this.clips.entries()) {
            const threeClip = clip.toThreeAnimationClip();
            const action = this.mixer.clipAction(threeClip);
            this.actions.set(name, action);
          }
        }
      }
    }
  }

  /**
   * 获取目标实体
   * @returns 实体
   */
  public getTargetEntity(): Entity | null {
    return this._targetEntity;
  }

  /**
   * 添加动画片段
   * @param clip 动画片段
   */
  public addClip(clip: AnimationClip): void {
    this.clips.set(clip.name, clip);

    // 如果有混合器，则创建动作
    if (this.mixer) {
      const threeClip = clip.toThreeAnimationClip();
      const action = this.mixer.clipAction(threeClip);
      this.actions.set(clip.name, action);
    }
  }

  /**
   * 移除动画片段
   * @param name 动画片段名称
   * @returns 是否成功移除
   */
  public removeClip(name: string): boolean {
    // 如果正在播放该片段，则停止播放
    if (this.currentClip && this.currentClip.name === name) {
      this.stop();
    }

    // 如果有混合器，则移除动作
    if (this.mixer) {
      const action = this.actions.get(name);
      if (action) {
        action.stop();
        this.actions.delete(name);
      }
    }

    return this.clips.delete(name);
  }

  /**
   * 获取动画片段
   * @param name 动画片段名称
   * @returns 动画片段，如果不存在则返回null
   */
  public getClip(name: string): AnimationClip | null {
    return this.clips.get(name) || null;
  }

  /**
   * 获取所有动画片段
   * @returns 动画片段数组
   */
  public getClips(): AnimationClip[] {
    return Array.from(this.clips.values());
  }

  /**
   * 播放动画
   * @param name 动画片段名称
   * @param blendTime 混合时间（秒），如果为0则立即切换
   * @returns 是否成功开始播放
   */
  public play(name: string, blendTime?: number): boolean {
    const clip = this.clips.get(name);
    if (!clip) {
      console.warn(`动画片段 "${name}" 不存在`);
      return false;
    }

    // 如果已经在播放该片段，则不做任何操作
    if (this.currentClip && this.currentClip.name === name && this.state === AnimationState.PLAYING) {
      return true;
    }

    // 设置混合时间
    const actualBlendTime = blendTime !== undefined ? blendTime : this.defaultBlendTime;

    // 如果当前没有播放任何片段，或者混合时间为0，则直接播放
    if (!this.currentClip || actualBlendTime <= 0) {
      this.currentClip = clip;
      this.time = 0;
      this.state = AnimationState.PLAYING;
      this.loop = this.currentClip.loopMode !== LoopMode.NONE;

      // 如果有混合器，则播放对应的动作
      if (this.mixer) {
        const action = this.actions.get(name);
        if (action) {
          action.reset();
          action.play();

          // 设置循环模式
          action.loop = this.currentClip.loopMode === LoopMode.PING_PONG ? THREE.LoopPingPong :
            this.currentClip.loopMode === LoopMode.REPEAT ? THREE.LoopRepeat :
            THREE.LoopOnce;

          // 设置权重和时间缩放
          action.weight = 1;
          action.timeScale = this.timeScale * this.currentClip.speed;

          // 停止其他所有动作
          for (const [otherName, otherAction] of this.actions.entries()) {
            if (otherName !== name) {
              otherAction.stop();
            }
          }
        }
      }

      this.eventEmitter.emit(AnimationEventType.START, { name });
      return true;
    }

    // 否则，设置为混合状态
    this.nextClip = clip;
    this.blendStartTime = this.time;
    this.blendTime = actualBlendTime;
    this.blendFactor = 0;
    this.state = AnimationState.BLENDING;

    // 如果有混合器，则混合到下一个动作
    if (this.mixer) {
      const currentAction = this.actions.get(this.currentClip.name);
      const nextAction = this.actions.get(name);

      if (currentAction && nextAction) {
        nextAction.reset();
        nextAction.play();

        // 设置循环模式
        nextAction.loop = this.nextClip.loopMode === LoopMode.PING_PONG ? THREE.LoopPingPong :
          this.nextClip.loopMode === LoopMode.REPEAT ? THREE.LoopRepeat :
          THREE.LoopOnce;

        // 设置权重和时间缩放
        nextAction.weight = 0;
        nextAction.timeScale = this.timeScale * this.nextClip.speed;

        // 交叉淡入淡出
        currentAction.crossFadeTo(nextAction, actualBlendTime, true);
      }
    }

    this.eventEmitter.emit(AnimationEventType.BLEND_START, { from: this.currentClip.name, to: name });
    return true;
  }

  /**
   * 停止播放
   */
  public stop(): void {
    if (this.state === AnimationState.STOPPED) {
      return;
    }

    const previousClip = this.currentClip;

    this.state = AnimationState.STOPPED;
    this.time = 0;
    this.blendFactor = 0;
    this.nextClip = null;

    // 如果有混合器，则停止所有动作
    if (this.mixer) {
      for (const action of this.actions.values()) {
        action.stop();
      }
    }

    if (previousClip) {
      this.eventEmitter.emit(AnimationEventType.STOP, { name: previousClip.name });
    }
  }

  /**
   * 暂停播放
   */
  public pause(): void {
    if (this.state !== AnimationState.PLAYING && this.state !== AnimationState.BLENDING) {
      return;
    }

    this.state = AnimationState.PAUSED;

    // 如果有混合器，则暂停所有动作
    if (this.mixer) {
      for (const action of this.actions.values()) {
        if (action.isRunning()) {
          action.paused = true;
        }
      }
    }

    if (this.currentClip) {
      this.eventEmitter.emit(AnimationEventType.PAUSE, { name: this.currentClip.name });
    }
  }

  /**
   * 恢复播放
   */
  public resume(): void {
    if (this.state !== AnimationState.PAUSED) {
      return;
    }

    this.state = this.nextClip ? AnimationState.BLENDING : AnimationState.PLAYING;

    // 如果有混合器，则恢复所有动作
    if (this.mixer) {
      for (const action of this.actions.values()) {
        if (action.paused) {
          action.paused = false;
        }
      }
    }

    if (this.currentClip) {
      this.eventEmitter.emit(AnimationEventType.RESUME, { name: this.currentClip.name });
    }
  }

  /**
   * 设置播放时间
   * @param time 时间（秒）
   */
  public setTime(time: number): void {
    this.time = time;

    // 如果有混合器，则设置所有动作的时间
    if (this.mixer) {
      this.mixer.setTime(time);
    }

    // 更新动画状态
    this.updateAnimationState();
  }

  /**
   * 获取播放时间
   * @returns 时间（秒）
   */
  public getTime(): number {
    return this.time;
  }

  /**
   * 设置时间缩放
   * @param timeScale 时间缩放
   */
  public setTimeScale(timeScale: number): void {
    this.timeScale = timeScale;

    // 如果有混合器，则设置所有动作的时间缩放
    if (this.mixer) {
      for (const [name, action] of this.actions.entries()) {
        const clip = this.clips.get(name);
        if (clip) {
          action.timeScale = timeScale * clip.speed;
        }
      }
    }
  }

  /**
   * 获取时间缩放
   * @returns 时间缩放
   */
  public getTimeScale(): number {
    return this.timeScale;
  }

  /**
   * 设置循环模式
   * @param loop 是否循环
   */
  public setLoop(loop: boolean): void {
    this.loop = loop;

    // 如果有当前片段，则更新其循环模式
    if (this.currentClip) {
      this.currentClip.loopMode = loop ? LoopMode.REPEAT : LoopMode.NONE;

      // 如果有混合器，则更新当前动作的循环模式
      if (this.mixer) {
        const action = this.actions.get(this.currentClip.name);
        if (action) {
          action.loop = loop ? THREE.LoopRepeat : THREE.LoopOnce;
        }
      }
    }
  }

  /**
   * 获取循环模式
   * @returns 是否循环
   */
  public getLoop(): boolean {
    return this.loop;
  }

  /**
   * 获取当前动画状态
   * @returns 动画状态
   */
  public getState(): AnimationState {
    return this.state;
  }

  /**
   * 获取当前播放的动画片段
   * @returns 动画片段，如果没有则返回null
   */
  public getCurrentClip(): AnimationClip | null {
    return this.currentClip;
  }

  /**
   * 获取下一个要播放的动画片段
   * @returns 动画片段，如果没有则返回null
   */
  public getNextClip(): AnimationClip | null {
    return this.nextClip;
  }

  /**
   * 获取混合因子
   * @returns 混合因子（0-1）
   */
  public getBlendFactor(): number {
    return this.blendFactor;
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器函数
   */
  public addListener(type: AnimationEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器函数
   */
  public removeListener(type: AnimationEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(type, listener);
  }

  /**
   * 更新动画
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (this.state === AnimationState.STOPPED || this.state === AnimationState.PAUSED) {
      return;
    }

    // 更新时间
    const scaledDeltaTime = deltaTime * this.timeScale;
    const previousTime = this.time;
    this.time += scaledDeltaTime;

    // 如果有混合器，则更新混合器
    if (this.mixer) {
      this.mixer.update(scaledDeltaTime);
    } else {
      // 否则，手动更新动画状态
      // 标记需要更新
      this._needsUpdate = true;
      this.updateAnimationState();
    }

    // 处理混合状态
    let blendStateChanged = false;
    if (this.state === AnimationState.BLENDING && this.nextClip) {
      // 计算混合因子
      const previousBlendFactor = this.blendFactor;
      const blendProgress = (this.time - this.blendStartTime) / this.blendTime;
      this.blendFactor = Math.min(blendProgress, 1.0);

      // 检查混合因子是否发生显著变化
      if (Math.abs(this.blendFactor - previousBlendFactor) > 0.01) {
        this._needsUpdate = true;
        blendStateChanged = true;
      }

      // 如果混合完成，则切换到下一个片段
      if (this.blendFactor >= 1.0) {
        const previousClip = this.currentClip;
        this.currentClip = this.nextClip;
        this.nextClip = null;
        this.state = AnimationState.PLAYING;
        this.loop = this.currentClip.loopMode !== LoopMode.NONE;
        this._needsUpdate = true;

        if (previousClip) {
          this.eventEmitter.emit(AnimationEventType.BLEND_COMPLETE, {
            from: previousClip.name,
            to: this.currentClip.name
          });
        }
      }
    }

    // 处理非循环动画的结束
    if (this.state === AnimationState.PLAYING && this.currentClip && !this.loop) {
      if (this.time >= this.currentClip.duration) {
        const clipName = this.currentClip.name;
        this.stop();
        this.eventEmitter.emit(AnimationEventType.COMPLETE, { name: clipName });
        return; // 已停止，不需要继续处理
      }
    }

    // 处理循环动画的循环事件
    if (this.state === AnimationState.PLAYING && this.currentClip && this.loop) {
      const duration = this.currentClip.duration;
      if (duration > 0) {
        const previousCycle = Math.floor(previousTime / duration);
        const currentCycle = Math.floor(this.time / duration);

        if (currentCycle > previousCycle) {
          this.eventEmitter.emit(AnimationEventType.LOOP, {
            name: this.currentClip.name,
            cycle: currentCycle
          });
          this._needsUpdate = true; // 循环时需要更新
        }
      }
    }

    // 如果时间变化显著且不在混合状态，则标记需要更新
    if (!blendStateChanged && this.state === AnimationState.PLAYING) {
      const timeDiff = this.time - previousTime;
      if (Math.abs(timeDiff) > 0.016) { // 大于一帧的时间
        this._needsUpdate = true;
      }
    }
  }

  /**
   * 设置是否启用缓存
   * @param enabled 是否启用
   */
  public setCacheEnabled(enabled: boolean): void {
    this._cacheEnabled = enabled;
    if (!enabled) {
      this._cachedTime = -1;
      this._cachedBlendFactor = -1;
      this._cachedAnimationState = null;
    }
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this._cachedTime = -1;
    this._cachedBlendFactor = -1;
    this._cachedAnimationState = null;
    this._needsUpdate = true;
  }

  /**
   * 获取Three.js动画混合器
   * @returns 动画混合器，如果不存在则返回null
   */
  public getMixer(): THREE.AnimationMixer | null {
    return this.mixer;
  }

  /**
   * 获取指定名称的动画动作
   * @param name 动画片段名称
   * @returns 动画动作，如果不存在则返回null
   */
  public getAction(name: string): THREE.AnimationAction | null {
    return this.actions.get(name) || null;
  }

  /**
   * 设置动画参数
   * @param name 参数名称
   * @param value 参数值
   */
  public setParameter(name: string, value: any): void {
    this.parameters.set(name, value);
  }

  /**
   * 获取动画参数
   * @param name 参数名称
   * @returns 参数值
   */
  public getParameter(name: string): any {
    return this.parameters.get(name);
  }

  /**
   * 获取所有动画参数
   * @returns 参数映射
   */
  public getParameters(): Map<string, any> {
    return new Map(this.parameters);
  }

  /**
   * 获取骨骼
   * @returns 骨骼，如果不存在则返回null
   */
  public getSkeleton(): THREE.Skeleton | null {
    if (!this._targetEntity) return null;

    const transform = this._targetEntity.getComponent('Transform');
    if (!transform) return null;

    const transformWithObject3D = transform as any;
    if (typeof transformWithObject3D.getObject3D === 'function') {
      const object3D = transformWithObject3D.getObject3D();
      if (object3D && object3D.type === 'SkinnedMesh') {
        return (object3D as THREE.SkinnedMesh).skeleton;
      }
    }

    return null;
  }

  /**
   * 更新动画片段
   * @param name 动画片段名称
   * @param clip 新的动画片段
   * @returns 是否成功更新
   */
  public updateClip(name: string, clip: AnimationClip): boolean {
    // 检查片段是否存在
    if (!this.clips.has(name)) {
      console.warn(`动画片段 "${name}" 不存在，无法更新`);
      return false;
    }

    // 更新片段
    this.clips.set(name, clip);

    // 如果有混合器，则更新对应的动作
    if (this.mixer) {
      // 移除旧动作
      const oldAction = this.actions.get(name);
      if (oldAction) {
        oldAction.stop();
        this.actions.delete(name);
      }

      // 创建新动作
      const threeClip = clip.toThreeAnimationClip();
      const newAction = this.mixer.clipAction(threeClip);
      this.actions.set(name, newAction);

      // 如果当前正在播放该片段，则重新开始播放
      if (this.currentClip && this.currentClip.name === name) {
        this.currentClip = clip;
        newAction.reset();
        newAction.play();
        newAction.weight = 1;
        newAction.timeScale = this.timeScale * clip.speed;
      }
    }

    return true;
  }

  /**
   * 更新动作映射
   * @param clipName 动画片段名称
   * @param action 新的动作
   */
  public updateAction(clipName: string, action: THREE.AnimationAction): void {
    this.actions.set(clipName, action);
  }

  /**
   * 更新动画状态
   */
  private updateAnimationState(): void {
    // 如果不需要更新且缓存有效，则直接使用缓存
    if (!this._needsUpdate && this._cacheEnabled &&
        this._cachedTime === this.time &&
        this._cachedBlendFactor === this.blendFactor &&
        this._cachedAnimationState) {
      // 复制缓存的状态
      this.animationState.clear();
      for (const [key, value] of this._cachedAnimationState) {
        this.animationState.set(key, this._cloneValue(value));
      }

      // 应用动画状态到实体
      this.applyAnimationState();
      return;
    }

    // 清除之前的状态
    this.animationState.clear();

    // 如果没有当前片段，则不更新
    if (!this.currentClip) {
      return;
    }

    // 计算当前片段的时间
    let currentTime = this.time;
    const currentDuration = this.currentClip.duration;

    // 处理循环
    if (currentDuration > 0) {
      if (this.loop) {
        if (this.currentClip.loopMode === LoopMode.REPEAT) {
          currentTime = currentTime % currentDuration;
        } else if (this.currentClip.loopMode === LoopMode.PING_PONG) {
          const cycle = Math.floor(currentTime / currentDuration);
          currentTime = currentTime % currentDuration;
          if (cycle % 2 === 1) {
            currentTime = currentDuration - currentTime;
          }
        } else {
          currentTime = Math.min(currentTime, currentDuration);
        }
      } else {
        currentTime = Math.min(currentTime, currentDuration);
      }
    }

    // 获取当前片段在当前时间的状态
    this.currentClip.getStateAtTime(currentTime, this.animationState);

    // 如果在混合状态，则混合下一个片段的状态
    if (this.state === AnimationState.BLENDING && this.nextClip && this.blendFactor > 0) {
      // 计算下一个片段的时间
      let nextTime = this.time - this.blendStartTime;
      const nextDuration = this.nextClip.duration;

      // 处理循环
      if (nextDuration > 0) {
        if (this.nextClip.loopMode === LoopMode.REPEAT) {
          nextTime = nextTime % nextDuration;
        } else if (this.nextClip.loopMode === LoopMode.PING_PONG) {
          const cycle = Math.floor(nextTime / nextDuration);
          nextTime = nextTime % nextDuration;
          if (cycle % 2 === 1) {
            nextTime = nextDuration - nextTime;
          }
        } else {
          nextTime = Math.min(nextTime, nextDuration);
        }
      }

      // 获取下一个片段在当前时间的状态
      const nextState = new Map<string, any>();
      this.nextClip.getStateAtTime(nextTime, nextState);

      // 混合两个状态
      for (const [path, nextValue] of nextState.entries()) {
        const currentValue = this.animationState.get(path);

        if (currentValue !== undefined) {
          // 如果当前状态中有该路径，则混合值
          const blendedValue = this.blendValues(currentValue, nextValue, this.blendFactor);
          this.animationState.set(path, blendedValue);
        } else {
          // 如果当前状态中没有该路径，则直接使用下一个值
          this.animationState.set(path, nextValue);
        }
      }
    }

    // 更新缓存
    if (this._cacheEnabled) {
      this._cachedTime = this.time;
      this._cachedBlendFactor = this.blendFactor;
      this._cachedAnimationState = new Map();
      for (const [key, value] of this.animationState) {
        this._cachedAnimationState.set(key, this._cloneValue(value));
      }
    }

    // 重置更新标志
    this._needsUpdate = false;

    // 应用动画状态到实体
    this.applyAnimationState();
  }

  /**
   * 克隆值
   * @param value 要克隆的值
   * @returns 克隆的值
   */
  private _cloneValue(value: any): any {
    if (value instanceof THREE.Vector2) {
      return value.clone();
    } else if (value instanceof THREE.Vector3) {
      return value.clone();
    } else if (value instanceof THREE.Vector4) {
      return value.clone();
    } else if (value instanceof THREE.Quaternion) {
      return value.clone();
    } else if (value instanceof THREE.Color) {
      return value.clone();
    } else if (value instanceof THREE.Matrix4) {
      return value.clone();
    } else {
      return value;
    }
  }

  /**
   * 混合两个值
   * @param a 值A
   * @param b 值B
   * @param t 混合因子（0-1）
   * @returns 混合结果
   */
  private blendValues(a: any, b: any, t: number): any {
    // 根据值的类型选择不同的混合方法
    if (a instanceof THREE.Vector3 && b instanceof THREE.Vector3) {
      return new THREE.Vector3().copy(a).lerp(b, t);
    } else if (a instanceof THREE.Quaternion && b instanceof THREE.Quaternion) {
      return new THREE.Quaternion().copy(a).slerp(b, t);
    } else if (a instanceof THREE.Color && b instanceof THREE.Color) {
      return new THREE.Color().copy(a).lerp(b, t);
    } else if (typeof a === 'number' && typeof b === 'number') {
      return a + (b - a) * t;
    } else if (typeof a === 'boolean' && typeof b === 'boolean') {
      return t < 0.5 ? a : b;
    } else if (a && b && typeof a.lerp === 'function') {
      return a.clone().lerp(b, t);
    } else {
      return t < 0.5 ? a : b;
    }
  }

  /**
   * 应用动画状态到实体
   */
  private applyAnimationState(): void {
    if (!this._targetEntity) {
      return;
    }

    // 获取实体的对象
    const transform = this._targetEntity.getComponent('Transform');
    if (!transform) {
      return;
    }

    // 使用类型断言访问getObject3D方法
    const transformWithObject3D = transform as any;
    if (typeof transformWithObject3D.getObject3D !== 'function') {
      return;
    }

    const object3D = transformWithObject3D.getObject3D();
    if (!object3D) {
      return;
    }

    // 应用动画状态到对象
    for (const [path, value] of this.animationState.entries()) {
      // 解析路径
      const parts = path.split('.');
      let target: any = object3D;

      // 遍历路径，找到目标对象
      for (let i = 0; i < parts.length - 1; i++) {
        const part = parts[i];

        // 如果是骨骼名称，则在骨骼列表中查找
        if (target.type === 'SkinnedMesh' && target.skeleton && i === 0) {
          const bone = target.skeleton.bones.find((b: THREE.Bone) => b.name === part);
          if (bone) {
            target = bone;
            continue;
          }
        }

        // 否则，在子对象中查找
        if (target.children) {
          const child = target.children.find((c: THREE.Object3D) => c.name === part);
          if (child) {
            target = child;
            continue;
          }
        }

        // 如果是属性，则获取属性
        if (target[part] !== undefined) {
          target = target[part];
        } else {
          // 如果找不到目标，则跳过
          target = null;
          break;
        }
      }

      // 如果找到目标，则设置属性
      if (target) {
        const property = parts[parts.length - 1];
        target[property] = value;
      }
    }
  }



  /**
   * 销毁组件
   */
  public dispose(): void {
    this.stop();
    this.clips.clear();
    this.actions.clear();
    this.animationState.clear();
    this.eventEmitter.removeAllListeners();

    if (this.mixer) {
      this.mixer.stopAllAction();
      this.mixer.uncacheRoot(this.mixer.getRoot());
      this.mixer = null;
    }

    this._targetEntity = null;

    super.dispose();
  }
}