/**
 * 网格组件
 * 用于渲染3D网格
 */

import * as THREE from 'three';
import { Component } from '../core/Component';
import { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 材质类型枚举
 */
export enum MaterialType {
  BASIC = 'basic',
  STANDARD = 'standard',
  PHYSICAL = 'physical',
  TOON = 'toon',
  MATCAP = 'matcap',
  NORMAL = 'normal',
  DEPTH = 'depth',
  DISTANCE = 'distance',
  LAMBERT = 'lambert',
  PHONG = 'phong',
  SHADER = 'shader',
  SPRITE = 'sprite',
  POINTS = 'points',
  LINE = 'line',
  DASH = 'dash'
}

/**
 * 材质选项接口
 */
export interface MaterialOptions {
  /** 材质名称 */
  name?: string;
  /** 材质颜色 */
  color?: THREE.Color | string | number;
  /** 材质贴图 */
  map?: THREE.Texture;
  /** 是否透明 */
  transparent?: boolean;
  /** 透明度 */
  opacity?: number;
  /** 是否双面渲染 */
  side?: THREE.Side;
  /** 混合模式 */
  blending?: THREE.Blending;
  /** 是否启用深度测试 */
  depthTest?: boolean;
  /** 是否写入深度缓冲 */
  depthWrite?: boolean;
  /** 是否启用Alpha测试 */
  alphaTest?: number;
  /** 是否启用雾效 */
  fog?: boolean;
  /** 是否启用线框模式 */
  wireframe?: boolean;
}

/**
 * 网格组件配置
 */
export interface MeshComponentOptions {
  /** 几何体 */
  geometry?: THREE.BufferGeometry;
  /** 材质 */
  material?: THREE.Material | THREE.Material[];
  /** 材质类型（如果没有提供材质） */
  materialType?: MaterialType;
  /** 材质选项（如果没有提供材质） */
  materialOptions?: MaterialOptions;
  /** 是否可见 */
  visible?: boolean;
  /** 是否接收阴影 */
  receiveShadow?: boolean;
  /** 是否投射阴影 */
  castShadow?: boolean;
  /** 是否启用视锥体剔除 */
  frustumCulled?: boolean;
  /** 渲染顺序 */
  renderOrder?: number;
  /** 是否启用实例化渲染 */
  instanced?: boolean;
  /** 实例数量 */
  instanceCount?: number;
  /** 是否启用LOD */
  enableLOD?: boolean;
  /** LOD级别 */
  lodLevels?: {
    distance: number;
    geometry: THREE.BufferGeometry;
  }[];
}

/**
 * 网格组件
 * 用于渲染3D网格
 */
export class MeshComponent extends Component {
  /** 组件类型 */
  public static readonly type = 'MeshComponent';

  /** 网格对象 */
  public mesh: THREE.Mesh;

  /** 几何体 */
  public geometry: THREE.BufferGeometry;

  /** 材质 */
  public material: THREE.Material | THREE.Material[];

  /** 是否启用LOD */
  public enableLOD: boolean;

  /** LOD对象 */
  public lod: THREE.LOD | null = null;

  /** 事件发射器 */
  public events: EventEmitter = new EventEmitter();

  /** 是否启用实例化渲染 */
  public instanced: boolean;

  /** 实例数量 */
  public instanceCount: number;

  /**
   * 构造函数
   * @param options 选项
   */
  constructor(options: MeshComponentOptions = {}) {
    super(MeshComponent.type);

    // 设置几何体
    this.geometry = options.geometry || new THREE.BoxGeometry(1, 1, 1);

    // 设置材质
    if (options.material) {
      this.material = options.material;
    } else {
      // 创建默认材质
      const materialOptions = options.materialOptions || { color: 0xcccccc };

      // 创建Three.js材质
      this.material = new THREE.MeshStandardMaterial(materialOptions);
    }

    // 创建网格
    this.mesh = new THREE.Mesh(this.geometry, this.material);

    // 设置网格属性
    this.mesh.visible = options.visible !== undefined ? options.visible : true;
    this.mesh.receiveShadow = options.receiveShadow !== undefined ? options.receiveShadow : true;
    this.mesh.castShadow = options.castShadow !== undefined ? options.castShadow : true;
    this.mesh.frustumCulled = options.frustumCulled !== undefined ? options.frustumCulled : true;
    this.mesh.renderOrder = options.renderOrder || 0;

    // 设置实例化渲染
    this.instanced = options.instanced || false;
    this.instanceCount = options.instanceCount || 1;

    // 设置LOD
    this.enableLOD = options.enableLOD || false;
    if (this.enableLOD && options.lodLevels && options.lodLevels.length > 0) {
      this.setupLOD(options.lodLevels);
    }
  }

  /**
   * 设置LOD
   * @param lodLevels LOD级别
   */
  private setupLOD(lodLevels: { distance: number; geometry: THREE.BufferGeometry }[]): void {
    this.lod = new THREE.LOD();

    // 添加原始网格作为最高级别
    this.lod.addLevel(this.mesh, 0);

    // 添加其他LOD级别
    for (const level of lodLevels) {
      const lodMesh = new THREE.Mesh(level.geometry, this.material);
      lodMesh.castShadow = this.mesh.castShadow;
      lodMesh.receiveShadow = this.mesh.receiveShadow;
      this.lod.addLevel(lodMesh, level.distance);
    }
  }

  /**
   * 设置材质
   * @param material 材质
   */
  public setMaterial(material: THREE.Material | THREE.Material[]): void {
    this.material = material;
    this.mesh.material = material;
    this.events.emit('materialChanged', material);
  }

  /**
   * 设置几何体
   * @param geometry 几何体
   */
  public setGeometry(geometry: THREE.BufferGeometry): void {
    this.geometry = geometry;
    this.mesh.geometry = geometry;
    this.events.emit('geometryChanged', geometry);
  }

  /**
   * 获取包围盒
   * @param target 目标包围盒
   * @returns 包围盒
   */
  public getBoundingBox(target?: THREE.Box3): THREE.Box3 {
    const box = target || new THREE.Box3();
    this.geometry.computeBoundingBox();
    box.copy(this.geometry.boundingBox!).applyMatrix4(this.mesh.matrixWorld);
    return box;
  }

  /**
   * 获取包围球
   * @param target 目标包围球
   * @returns 包围球
   */
  public getBoundingSphere(target?: THREE.Sphere): THREE.Sphere {
    const sphere = target || new THREE.Sphere();
    this.geometry.computeBoundingSphere();
    sphere.copy(this.geometry.boundingSphere!);
    sphere.radius *= this.mesh.matrixWorld.getMaxScaleOnAxis();
    sphere.center.applyMatrix4(this.mesh.matrixWorld);
    return sphere;
  }
}
