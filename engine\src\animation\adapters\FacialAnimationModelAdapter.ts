/**
 * 面部动画模型适配器
 * 用于将面部动画系统与具体的3D模型绑定
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import { Component } from '../../core/Component';
import { FacialExpressionType, VisemeType } from '../FacialAnimation';

/**
 * 面部动画模型类型
 */
export enum FacialAnimationModelType {
  /** 通用模型 */
  GENERIC = 'generic',
  /** VRM模型 */
  VRM = 'vrm',
  /** FBX模型 */
  FBX = 'fbx',
  /** GLTF模型 */
  GLTF = 'gltf'
}

/**
 * 表情映射
 */
export interface ExpressionMapping {
  /** 表情类型 */
  expression: FacialExpressionType;
  /** 混合形状名称 */
  blendShapeName: string;
  /** 混合形状索引 */
  blendShapeIndex?: number;
  /** 权重缩放 */
  weightScale?: number;
  /** 权重偏移 */
  weightOffset?: number;
}

/**
 * 口型映射
 */
export interface VisemeMapping {
  /** 口型类型 */
  viseme: VisemeType;
  /** 混合形状名称 */
  blendShapeName: string;
  /** 混合形状索引 */
  blendShapeIndex?: number;
  /** 权重缩放 */
  weightScale?: number;
  /** 权重偏移 */
  weightOffset?: number;
}

/**
 * 面部动画模型适配器配置
 */
export interface FacialAnimationModelAdapterConfig {
  /** 模型类型 */
  modelType?: FacialAnimationModelType;
  /** 表情映射 */
  expressionMappings?: ExpressionMapping[];
  /** 口型映射 */
  visemeMappings?: VisemeMapping[];
  /** 是否自动检测混合形状 */
  autoDetectBlendShapes?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 面部动画模型适配器组件
 */
export class FacialAnimationModelAdapterComponent extends Component {
  /** 组件类型 */
  static readonly type = 'FacialAnimationModelAdapter';

  /** 模型类型 */
  private modelType: FacialAnimationModelType;
  /** 表情映射 */
  private expressionMappings: Map<FacialExpressionType, ExpressionMapping> = new Map();
  /** 口型映射 */
  private visemeMappings: Map<VisemeType, VisemeMapping> = new Map();
  /** 骨骼网格 */
  private skinnedMesh: THREE.SkinnedMesh | null = null;
  /** 是否启用调试 */
  private debug: boolean;
  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: FacialAnimationModelAdapterConfig = {}) {
    super(FacialAnimationModelAdapterComponent.type);

    this.modelType = config.modelType || FacialAnimationModelType.GENERIC;
    this.debug = config.debug || false;

    // 初始化表情映射
    if (config.expressionMappings) {
      for (const mapping of config.expressionMappings) {
        this.expressionMappings.set(mapping.expression, mapping);
      }
    }

    // 初始化口型映射
    if (config.visemeMappings) {
      for (const mapping of config.visemeMappings) {
        this.visemeMappings.set(mapping.viseme, mapping);
      }
    }

    // 如果启用自动检测，则在设置骨骼网格时自动检测混合形状
    if (config.autoDetectBlendShapes) {
      this.autoDetectBlendShapes();
    }
  }

  /**
   * 设置骨骼网格
   * @param mesh 骨骼网格
   */
  public setSkinnedMesh(mesh: THREE.SkinnedMesh): void {
    this.skinnedMesh = mesh;
    this.initialized = false;

    // 如果表情映射和口型映射为空，则自动检测混合形状
    if (this.expressionMappings.size === 0 && this.visemeMappings.size === 0) {
      this.autoDetectBlendShapes();
    }
  }

  /**
   * 获取骨骼网格
   * @returns 骨骼网格
   */
  public getSkinnedMesh(): THREE.SkinnedMesh | null {
    return this.skinnedMesh;
  }

  /**
   * 自动检测混合形状
   */
  public autoDetectBlendShapes(): void {
    if (!this.skinnedMesh) return;

    // 获取混合形状字典
    const morphTargetDictionary = this.skinnedMesh.morphTargetDictionary;
    if (!morphTargetDictionary) {
      if (this.debug) {
        console.warn('无法检测混合形状：模型没有morphTargetDictionary');
      }
      return;
    }

    // 根据模型类型检测混合形状
    switch (this.modelType) {
      case FacialAnimationModelType.VRM:
        this.detectVRMBlendShapes(morphTargetDictionary);
        break;
      case FacialAnimationModelType.FBX:
        this.detectFBXBlendShapes(morphTargetDictionary);
        break;
      case FacialAnimationModelType.GLTF:
        this.detectGLTFBlendShapes(morphTargetDictionary);
        break;
      default:
        this.detectGenericBlendShapes(morphTargetDictionary);
        break;
    }

    this.initialized = true;

    if (this.debug) {
      console.log('检测到的表情映射:', Array.from(this.expressionMappings.entries()));
      console.log('检测到的口型映射:', Array.from(this.visemeMappings.entries()));
    }
  }

  /**
   * 检测VRM混合形状
   * @param morphTargetDictionary 混合形状字典
   */
  private detectVRMBlendShapes(morphTargetDictionary: { [key: string]: number }): void {
    // VRM表情映射
    const vrmExpressionMap: { [key: string]: FacialExpressionType } = {
      'neutral': FacialExpressionType.NEUTRAL,
      'happy': FacialExpressionType.HAPPY,
      'sad': FacialExpressionType.SAD,
      'angry': FacialExpressionType.ANGRY,
      'surprised': FacialExpressionType.SURPRISED,
      'relaxed': FacialExpressionType.NEUTRAL,
      'joy': FacialExpressionType.HAPPY,
      'sorrow': FacialExpressionType.SAD,
      'fun': FacialExpressionType.HAPPY,
      'aa': FacialExpressionType.SURPRISED,
      'ih': FacialExpressionType.HAPPY,
      'ou': FacialExpressionType.SURPRISED,
      'ee': FacialExpressionType.HAPPY,
      'oh': FacialExpressionType.SURPRISED
    };

    // VRM口型映射
    const vrmVisemeMap: { [key: string]: VisemeType } = {
      'aa': VisemeType.AA,
      'ih': VisemeType.IH,
      'ou': VisemeType.OU,
      'ee': VisemeType.EE,
      'oh': VisemeType.OH,
      'blink': VisemeType.SILENT,
      'blinkLeft': VisemeType.SILENT,
      'blinkRight': VisemeType.SILENT
    };

    // 检测表情
    for (const [name, index] of Object.entries(morphTargetDictionary)) {
      // 检查是否是表情
      const expressionType = vrmExpressionMap[name.toLowerCase()];
      if (expressionType) {
        this.expressionMappings.set(expressionType, {
          expression: expressionType,
          blendShapeName: name,
          blendShapeIndex: index,
          weightScale: 1.0,
          weightOffset: 0.0
        });
      }

      // 检查是否是口型
      const visemeType = vrmVisemeMap[name.toLowerCase()];
      if (visemeType) {
        this.visemeMappings.set(visemeType, {
          viseme: visemeType,
          blendShapeName: name,
          blendShapeIndex: index,
          weightScale: 1.0,
          weightOffset: 0.0
        });
      }
    }
  }

  /**
   * 检测FBX混合形状
   * @param morphTargetDictionary 混合形状字典
   */
  private detectFBXBlendShapes(morphTargetDictionary: { [key: string]: number }): void {
    // FBX表情映射
    const fbxExpressionMap: { [key: string]: FacialExpressionType } = {
      'neutral': FacialExpressionType.NEUTRAL,
      'happy': FacialExpressionType.HAPPY,
      'sad': FacialExpressionType.SAD,
      'angry': FacialExpressionType.ANGRY,
      'surprised': FacialExpressionType.SURPRISED,
      'smile': FacialExpressionType.HAPPY,
      'frown': FacialExpressionType.SAD,
      'brow_up': FacialExpressionType.SURPRISED,
      'brow_down': FacialExpressionType.ANGRY
    };

    // FBX口型映射
    const fbxVisemeMap: { [key: string]: VisemeType } = {
      'mouth_open': VisemeType.AA,
      'mouth_wide': VisemeType.IH,
      'mouth_round': VisemeType.OU,
      'mouth_smile': VisemeType.EE,
      'mouth_frown': VisemeType.OH
    };

    // 检测表情和口型
    for (const [name, index] of Object.entries(morphTargetDictionary)) {
      // 检查是否是表情
      for (const [key, value] of Object.entries(fbxExpressionMap)) {
        if (name.toLowerCase().includes(key)) {
          this.expressionMappings.set(value, {
            expression: value,
            blendShapeName: name,
            blendShapeIndex: index,
            weightScale: 1.0,
            weightOffset: 0.0
          });
          break;
        }
      }

      // 检查是否是口型
      for (const [key, value] of Object.entries(fbxVisemeMap)) {
        if (name.toLowerCase().includes(key)) {
          this.visemeMappings.set(value, {
            viseme: value,
            blendShapeName: name,
            blendShapeIndex: index,
            weightScale: 1.0,
            weightOffset: 0.0
          });
          break;
        }
      }
    }
  }

  /**
   * 检测GLTF混合形状
   * @param morphTargetDictionary 混合形状字典
   */
  private detectGLTFBlendShapes(morphTargetDictionary: { [key: string]: number }): void {
    // 使用通用检测方法
    this.detectGenericBlendShapes(morphTargetDictionary);
  }

  /**
   * 检测通用混合形状
   * @param morphTargetDictionary 混合形状字典
   */
  private detectGenericBlendShapes(morphTargetDictionary: { [key: string]: number }): void {
    // 通用表情映射
    const genericExpressionMap: { [key: string]: FacialExpressionType } = {
      'neutral': FacialExpressionType.NEUTRAL,
      'happy': FacialExpressionType.HAPPY,
      'sad': FacialExpressionType.SAD,
      'angry': FacialExpressionType.ANGRY,
      'surprised': FacialExpressionType.SURPRISED,
      'fear': FacialExpressionType.FEAR,
      'disgust': FacialExpressionType.DISGUST,
      'smile': FacialExpressionType.HAPPY,
      'frown': FacialExpressionType.SAD,
      'joy': FacialExpressionType.HAPPY,
      'sorrow': FacialExpressionType.SAD
    };

    // 通用口型映射
    const genericVisemeMap: { [key: string]: VisemeType } = {
      'viseme_aa': VisemeType.AA,
      'viseme_ee': VisemeType.EE,
      'viseme_ih': VisemeType.IH,
      'viseme_oh': VisemeType.OH,
      'viseme_ou': VisemeType.OU,
      'viseme_pp': VisemeType.PP,
      'viseme_ff': VisemeType.FF,
      'viseme_th': VisemeType.TH,
      'viseme_dd': VisemeType.DD,
      'viseme_kk': VisemeType.KK,
      'viseme_ch': VisemeType.CH,
      'viseme_ss': VisemeType.SS,
      'viseme_nn': VisemeType.NN,
      'viseme_rr': VisemeType.RR,
      'viseme_silent': VisemeType.SILENT,
      'mouth_open': VisemeType.AA,
      'mouth_wide': VisemeType.IH,
      'mouth_round': VisemeType.OU,
      'mouth_smile': VisemeType.EE,
      'mouth_frown': VisemeType.OH
    };

    // 检测表情和口型
    for (const [name, index] of Object.entries(morphTargetDictionary)) {
      const lowerName = name.toLowerCase();

      // 检查是否是表情
      for (const [key, value] of Object.entries(genericExpressionMap)) {
        if (lowerName.includes(key)) {
          this.expressionMappings.set(value, {
            expression: value,
            blendShapeName: name,
            blendShapeIndex: index,
            weightScale: 1.0,
            weightOffset: 0.0
          });
          break;
        }
      }

      // 检查是否是口型
      for (const [key, value] of Object.entries(genericVisemeMap)) {
        if (lowerName.includes(key)) {
          this.visemeMappings.set(value, {
            viseme: value,
            blendShapeName: name,
            blendShapeIndex: index,
            weightScale: 1.0,
            weightOffset: 0.0
          });
          break;
        }
      }
    }
  }

  /**
   * 应用表情
   * @param expression 表情类型
   * @param weight 权重
   * @returns 是否成功应用
   */
  public applyExpression(expression: FacialExpressionType, weight: number): boolean {
    if (!this.skinnedMesh || !this.initialized) return false;

    // 获取表情映射
    const mapping = this.expressionMappings.get(expression);
    if (!mapping) return false;

    // 应用混合形状权重
    const morphTargetInfluences = this.skinnedMesh.morphTargetInfluences;
    if (!morphTargetInfluences) return false;

    // 计算实际权重
    const actualWeight = weight * (mapping.weightScale || 1.0) + (mapping.weightOffset || 0.0);

    // 设置混合形状权重
    if (mapping.blendShapeIndex !== undefined && mapping.blendShapeIndex >= 0 && mapping.blendShapeIndex < morphTargetInfluences.length) {
      morphTargetInfluences[mapping.blendShapeIndex] = actualWeight;
      return true;
    }

    return false;
  }

  /**
   * 应用口型
   * @param viseme 口型类型
   * @param weight 权重
   * @returns 是否成功应用
   */
  public applyViseme(viseme: VisemeType, weight: number): boolean {
    if (!this.skinnedMesh || !this.initialized) return false;

    // 获取口型映射
    const mapping = this.visemeMappings.get(viseme);
    if (!mapping) return false;

    // 应用混合形状权重
    const morphTargetInfluences = this.skinnedMesh.morphTargetInfluences;
    if (!morphTargetInfluences) return false;

    // 计算实际权重
    const actualWeight = weight * (mapping.weightScale || 1.0) + (mapping.weightOffset || 0.0);

    // 设置混合形状权重
    if (mapping.blendShapeIndex !== undefined && mapping.blendShapeIndex >= 0 && mapping.blendShapeIndex < morphTargetInfluences.length) {
      morphTargetInfluences[mapping.blendShapeIndex] = actualWeight;
      return true;
    }

    return false;
  }

  /**
   * 重置所有表情和口型
   */
  public resetAll(): void {
    if (!this.skinnedMesh || !this.initialized) return;

    // 重置所有混合形状权重
    const morphTargetInfluences = this.skinnedMesh.morphTargetInfluences;
    if (!morphTargetInfluences) return;

    // 重置表情
    for (const mapping of this.expressionMappings.values()) {
      if (mapping.blendShapeIndex !== undefined && mapping.blendShapeIndex >= 0 && mapping.blendShapeIndex < morphTargetInfluences.length) {
        morphTargetInfluences[mapping.blendShapeIndex] = 0;
      }
    }

    // 重置口型
    for (const mapping of this.visemeMappings.values()) {
      if (mapping.blendShapeIndex !== undefined && mapping.blendShapeIndex >= 0 && mapping.blendShapeIndex < morphTargetInfluences.length) {
        morphTargetInfluences[mapping.blendShapeIndex] = 0;
      }
    }
  }
}
