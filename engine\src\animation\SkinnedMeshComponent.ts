/**
 * 蒙皮网格组件
 * 用于管理蒙皮网格对象
 */

import * as THREE from 'three';
import { Component } from '../core/Component';

export class SkinnedMeshComponent extends Component {
  public static readonly type: string = 'SkinnedMeshComponent';

  private skinnedMesh: THREE.SkinnedMesh | null = null;
  private skeleton: THREE.Skeleton | null = null;
  private bones: THREE.Bone[] = [];

  constructor() {
    super(SkinnedMeshComponent.type);
  }

  public setSkinnedMesh(mesh: THREE.SkinnedMesh): void {
    this.skinnedMesh = mesh;
    this.skeleton = mesh.skeleton;
    this.bones = mesh.skeleton.bones;
  }

  public getSkinnedMesh(): THREE.SkinnedMesh | null {
    return this.skinnedMesh;
  }

  public getSkeleton(): THREE.Skeleton | null {
    return this.skeleton;
  }

  public getBones(): THREE.Bone[] {
    return this.bones;
  }

  public getBone(name: string): THREE.Bone | null {
    return this.bones.find(bone => bone.name === name) || null;
  }

  public getBoneByIndex(index: number): THREE.Bone | null {
    return this.bones[index] || null;
  }

  public update(deltaTime: number): void {
    // 更新骨骼动画
    if (this.skeleton) {
      this.skeleton.update();
    }
  }

  public dispose(): void {
    if (this.skinnedMesh) {
      this.skinnedMesh.geometry.dispose();
      if (Array.isArray(this.skinnedMesh.material)) {
        this.skinnedMesh.material.forEach(material => material.dispose());
      } else {
        this.skinnedMesh.material.dispose();
      }
    }
    super.dispose();
  }
}
