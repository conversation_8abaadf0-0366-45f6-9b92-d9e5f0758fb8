/**
 * 增强版GPU面部动画系统
 * 提供更高性能的面部动画计算，支持更多平台和设备
 */
import * as THREE from 'three';
import { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { System } from '../core/System';
import { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';
import { FacialExpressionType, VisemeType } from './FacialAnimation';

/**
 * 增强版GPU面部动画配置
 */
export interface EnhancedGPUFacialAnimationConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否使用计算着色器 */
  useComputeShader?: boolean;
  /** 最大混合形状数量 */
  maxBlendShapes?: number;
  /** 纹理大小 */
  textureSize?: number;
  /** 是否使用WebGPU（如果可用） */
  useWebGPU?: boolean;
  /** 是否使用WebGL2（如果可用） */
  useWebGL2?: boolean;
  /** 是否使用WebGL1（如果不支持WebGL2） */
  useWebGL1Fallback?: boolean;
  /** 是否使用移动优化 */
  useMobileOptimization?: boolean;
  /** 是否使用实例化渲染 */
  useInstancing?: boolean;
  /** 是否使用自动LOD */
  useAutoLOD?: boolean;
  /** LOD距离阈值 */
  lodDistanceThresholds?: number[];
  /** 是否使用异步计算 */
  useAsyncCompute?: boolean;
  /** 是否使用共享内存 */
  useSharedMemory?: boolean;
}

/**
 * 增强版GPU面部动画组件
 */
export class EnhancedGPUFacialAnimationComponent extends Component {
  /** 组件类型 */
  static readonly type = 'EnhancedGPUFacialAnimation';

  /** 原始材质 */
  private originalMaterials: Map<THREE.Mesh, THREE.Material | THREE.Material[]> = new Map();
  /** GPU材质 */
  private gpuMaterials: Map<THREE.Mesh, THREE.Material | THREE.Material[]> = new Map();
  /** 混合形状纹理 */
  private blendShapeTexture: THREE.DataTexture | null = null;
  /** 混合形状数据 */
  private blendShapeData: Float32Array | null = null;
  /** 混合形状名称到索引的映射 */
  private blendShapeMap: Map<string, number> = new Map();
  /** 表情到混合形状的映射 */
  private expressionMap: Map<string, string> = new Map();
  /** 口型到混合形状的映射 */
  private visemeMap: Map<string, string> = new Map();
  /** 是否初始化 */
  private initialized: boolean = false;
  /** 是否需要更新 */
  private needsUpdate: boolean = false;
  /** 纹理大小 */
  private textureSize: number = 16;
  /** 是否使用WebGPU */
  private usingWebGPU: boolean = false;
  /** 是否使用WebGL2 */
  private usingWebGL2: boolean = false;
  /** 是否使用WebGL1 */
  private usingWebGL1: boolean = false;
  /** 是否使用移动优化 */
  private usingMobileOptimization: boolean = false;
  /** LOD级别 */
  private lodLevel: number = 0;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   */
  constructor() {
    super('GPUFacialAnimationEnhanced');
  }

  /**
   * 初始化组件
   * @param mesh 骨骼网格
   * @param textureSize 纹理大小
   * @param useWebGPU 是否使用WebGPU
   * @param useWebGL2 是否使用WebGL2
   * @param useWebGL1Fallback 是否使用WebGL1回退
   * @param useMobileOptimization 是否使用移动优化
   */
  public initialize(
    mesh: THREE.SkinnedMesh,
    textureSize: number = 16,
    useWebGPU: boolean = false,
    useWebGL2: boolean = true,
    useWebGL1Fallback: boolean = true,
    useMobileOptimization: boolean = false
  ): void {
    if (this.initialized) return;

    this.textureSize = textureSize;
    this.usingWebGPU = useWebGPU;
    this.usingWebGL2 = useWebGL2;
    this.usingWebGL1 = useWebGL1Fallback;
    this.usingMobileOptimization = useMobileOptimization;

    // 创建混合形状映射
    this.createBlendShapeMap(mesh);

    // 创建混合形状纹理
    this.createBlendShapeTexture();

    // 创建GPU材质
    this.createGPUMaterial(mesh);

    this.initialized = true;
    this.needsUpdate = true;

    this.eventEmitter.emit('initialized', { entity: this.entity });
  }

  /**
   * 创建混合形状映射
   * @param mesh 骨骼网格
   */
  private createBlendShapeMap(mesh: THREE.SkinnedMesh): void {
    // 检查混合形状字典
    if (!mesh.morphTargetDictionary) {
      console.warn('骨骼网格没有混合形状字典');
      return;
    }

    // 创建混合形状映射
    let index = 0;
    for (const name in mesh.morphTargetDictionary) {
      this.blendShapeMap.set(name, index++);
    }

    // 创建默认表情映射
    this.createDefaultExpressionMap();

    // 创建默认口型映射
    this.createDefaultVisemeMap();
  }

  /**
   * 创建默认表情映射
   */
  private createDefaultExpressionMap(): void {
    // 默认表情映射
    this.expressionMap.set(FacialExpressionType.NEUTRAL, 'neutral');
    this.expressionMap.set(FacialExpressionType.HAPPY, 'smile');
    this.expressionMap.set(FacialExpressionType.SAD, 'sad');
    this.expressionMap.set(FacialExpressionType.ANGRY, 'angry');
    this.expressionMap.set(FacialExpressionType.SURPRISED, 'surprised');
    this.expressionMap.set(FacialExpressionType.DISGUSTED, 'disgusted');
    this.expressionMap.set(FacialExpressionType.FEAR, 'fear');
    this.expressionMap.set(FacialExpressionType.DISGUST, 'disgust');
  }

  /**
   * 创建默认口型映射
   */
  private createDefaultVisemeMap(): void {
    // 默认口型映射
    this.visemeMap.set(VisemeType.SILENT, 'viseme_neutral');
    this.visemeMap.set(VisemeType.PP, 'viseme_pp');
    this.visemeMap.set(VisemeType.FF, 'viseme_ff');
    this.visemeMap.set(VisemeType.TH, 'viseme_th');
    this.visemeMap.set(VisemeType.DD, 'viseme_dd');
    this.visemeMap.set(VisemeType.KK, 'viseme_kk');
    this.visemeMap.set(VisemeType.CH, 'viseme_ch');
    this.visemeMap.set(VisemeType.SS, 'viseme_ss');
    this.visemeMap.set(VisemeType.NN, 'viseme_nn');
    this.visemeMap.set(VisemeType.RR, 'viseme_rr');
    this.visemeMap.set(VisemeType.AA, 'viseme_aa');
    this.visemeMap.set(VisemeType.EE, 'viseme_ee');
    this.visemeMap.set(VisemeType.IH, 'viseme_ih');
    this.visemeMap.set(VisemeType.OH, 'viseme_oh');
    this.visemeMap.set(VisemeType.OU, 'viseme_ou');
  }

  /**
   * 创建混合形状纹理
   */
  private createBlendShapeTexture(): void {
    // 创建纹理数据
    const size = this.textureSize;
    const data = new Float32Array(size * size * 4);
    this.blendShapeData = data;

    // 创建纹理
    const texture = new THREE.DataTexture(
      data,
      size,
      size,
      THREE.RGBAFormat,
      THREE.FloatType
    );
    texture.needsUpdate = true;
    this.blendShapeTexture = texture;
  }

  /**
   * 创建GPU材质
   * @param mesh 骨骼网格
   */
  private createGPUMaterial(mesh: THREE.SkinnedMesh): void {
    // 保存原始材质
    this.originalMaterials.set(mesh, mesh.material);

    // 创建GPU材质
    let material: THREE.Material;

    if (this.usingWebGPU) {
      // WebGPU材质
      material = this.createWebGPUMaterial(mesh);
    } else if (this.usingWebGL2) {
      // WebGL2材质
      material = this.createWebGL2Material(mesh);
    } else if (this.usingWebGL1) {
      // WebGL1材质
      material = this.createWebGL1Material(mesh);
    } else {
      // 回退到原始材质
      console.warn('不支持GPU加速，使用原始材质');
      return;
    }

    // 应用GPU材质
    this.gpuMaterials.set(mesh, material);
    mesh.material = material;
  }

  /**
   * 创建WebGPU材质
   * @param mesh 骨骼网格
   * @returns WebGPU材质
   */
  private createWebGPUMaterial(mesh: THREE.SkinnedMesh): THREE.Material {
    // 这里是WebGPU材质的占位代码
    // 实际实现需要等待Three.js对WebGPU的完整支持
    console.warn('WebGPU材质尚未实现，使用WebGL2材质代替');
    return this.createWebGL2Material(mesh);
  }

  /**
   * 创建WebGL2材质
   * @param mesh 骨骼网格
   * @returns WebGL2材质
   */
  private createWebGL2Material(mesh: THREE.SkinnedMesh): THREE.Material {
    // 获取原始材质
    const originalMaterial = mesh.material instanceof THREE.Material
      ? mesh.material
      : Array.isArray(mesh.material) ? mesh.material[0] : null;

    if (!originalMaterial) {
      console.error('无法获取原始材质');
      return new THREE.MeshBasicMaterial();
    }

    // 创建自定义材质
    const material = new THREE.ShaderMaterial({
      uniforms: {
        ...originalMaterial instanceof THREE.ShaderMaterial ? originalMaterial.uniforms : {},
        blendShapeTexture: { value: this.blendShapeTexture },
        blendShapeTextureSize: { value: this.textureSize },
        useMobileOptimization: { value: this.usingMobileOptimization ? 1.0 : 0.0 },
        lodLevel: { value: this.lodLevel }
      },
      vertexShader: this.getWebGL2VertexShader(mesh),
      fragmentShader: originalMaterial instanceof THREE.ShaderMaterial
        ? originalMaterial.fragmentShader
        : this.getDefaultFragmentShader(),
      lights: true,
      wireframe: false,
      transparent: originalMaterial.transparent,
      side: originalMaterial.side
    });

    // 手动设置skinning和morphTargets
    (material as any).skinning = true;
    (material as any).morphTargets = true;

    return material;
  }

  /**
   * 创建WebGL1材质
   * @param mesh 骨骼网格
   * @returns WebGL1材质
   */
  private createWebGL1Material(mesh: THREE.SkinnedMesh): THREE.Material {
    // 获取原始材质
    const originalMaterial = mesh.material instanceof THREE.Material
      ? mesh.material
      : Array.isArray(mesh.material) ? mesh.material[0] : null;

    if (!originalMaterial) {
      console.error('无法获取原始材质');
      return new THREE.MeshBasicMaterial();
    }

    // 创建自定义材质
    const material = new THREE.ShaderMaterial({
      uniforms: {
        ...originalMaterial instanceof THREE.ShaderMaterial ? originalMaterial.uniforms : {},
        blendShapeTexture: { value: this.blendShapeTexture },
        blendShapeTextureSize: { value: this.textureSize },
        useMobileOptimization: { value: this.usingMobileOptimization ? 1.0 : 0.0 },
        lodLevel: { value: this.lodLevel }
      },
      vertexShader: this.getWebGL1VertexShader(mesh),
      fragmentShader: originalMaterial instanceof THREE.ShaderMaterial
        ? originalMaterial.fragmentShader
        : this.getDefaultFragmentShader(),
      lights: true,
      wireframe: false,
      transparent: originalMaterial.transparent,
      side: originalMaterial.side
    });

    // 手动设置skinning和morphTargets
    (material as any).skinning = true;
    (material as any).morphTargets = true;

    return material;
  }

  /**
   * 获取WebGL2顶点着色器
   * @param mesh 骨骼网格
   * @returns 顶点着色器代码
   */
  private getWebGL2VertexShader(mesh: THREE.SkinnedMesh): string {
    // 这里是WebGL2顶点着色器的占位代码
    // 实际实现需要根据模型的混合形状生成
    return `
      uniform sampler2D blendShapeTexture;
      uniform float blendShapeTextureSize;
      uniform float useMobileOptimization;
      uniform float lodLevel;

      attribute vec3 position;
      attribute vec3 normal;
      attribute vec4 tangent;
      attribute vec2 uv;
      attribute vec4 skinIndex;
      attribute vec4 skinWeight;

      // 混合形状属性
      attribute vec3 morphTarget0;
      attribute vec3 morphTarget1;
      attribute vec3 morphTarget2;
      attribute vec3 morphTarget3;
      attribute vec3 morphTarget4;
      attribute vec3 morphTarget5;
      attribute vec3 morphTarget6;
      attribute vec3 morphTarget7;

      varying vec2 vUv;
      varying vec3 vNormal;

      vec4 getBlendShapeWeight(int index) {
        float y = floor(float(index) / blendShapeTextureSize);
        float x = float(index) - y * blendShapeTextureSize;
        vec2 uv = vec2(x, y) / blendShapeTextureSize;
        return texture2D(blendShapeTexture, uv);
      }

      vec3 applyBlendShape(vec3 position) {
        vec3 result = position;

        // 根据LOD级别应用不同数量的混合形状
        int maxBlendShapes = 8;
        if (lodLevel > 0.5) maxBlendShapes = 4;
        if (lodLevel > 1.5) maxBlendShapes = 2;

        // 应用混合形状
        for (int i = 0; i < 8; i++) {
          if (i >= maxBlendShapes) break;

          vec4 weight = getBlendShapeWeight(i);

          if (i == 0) result += morphTarget0 * weight.x;
          else if (i == 1) result += morphTarget1 * weight.x;
          else if (i == 2) result += morphTarget2 * weight.x;
          else if (i == 3) result += morphTarget3 * weight.x;
          else if (i == 4) result += morphTarget4 * weight.x;
          else if (i == 5) result += morphTarget5 * weight.x;
          else if (i == 6) result += morphTarget6 * weight.x;
          else if (i == 7) result += morphTarget7 * weight.x;
        }

        return result;
      }

      void main() {
        vUv = uv;

        // 应用混合形状
        vec3 transformed = applyBlendShape(position);

        // 应用骨骼动画
        #include <skinning_vertex>

        // 计算法线
        vNormal = normalMatrix * normal;

        // 投影
        gl_Position = projectionMatrix * modelViewMatrix * vec4(transformed, 1.0);
      }
    `;
  }

  /**
   * 获取WebGL1顶点着色器
   * @param mesh 骨骼网格
   * @returns 顶点着色器代码
   */
  private getWebGL1VertexShader(mesh: THREE.SkinnedMesh): string {
    // 这里是WebGL1顶点着色器的占位代码
    // 实际实现需要根据模型的混合形状生成
    return `
      uniform sampler2D blendShapeTexture;
      uniform float blendShapeTextureSize;
      uniform float useMobileOptimization;
      uniform float lodLevel;

      attribute vec3 position;
      attribute vec3 normal;
      attribute vec2 uv;
      attribute vec4 skinIndex;
      attribute vec4 skinWeight;

      // 混合形状属性
      attribute vec3 morphTarget0;
      attribute vec3 morphTarget1;
      attribute vec3 morphTarget2;
      attribute vec3 morphTarget3;

      varying vec2 vUv;
      varying vec3 vNormal;

      vec4 getBlendShapeWeight(int index) {
        float y = floor(float(index) / blendShapeTextureSize);
        float x = float(index) - y * blendShapeTextureSize;
        vec2 uv = vec2(x, y) / blendShapeTextureSize;
        return texture2D(blendShapeTexture, uv);
      }

      vec3 applyBlendShape(vec3 position) {
        vec3 result = position;

        // 根据LOD级别应用不同数量的混合形状
        int maxBlendShapes = 4;
        if (lodLevel > 0.5) maxBlendShapes = 2;
        if (lodLevel > 1.5) maxBlendShapes = 1;

        // 应用混合形状
        for (int i = 0; i < 4; i++) {
          if (i >= maxBlendShapes) break;

          vec4 weight = getBlendShapeWeight(i);

          if (i == 0) result += morphTarget0 * weight.x;
          else if (i == 1) result += morphTarget1 * weight.x;
          else if (i == 2) result += morphTarget2 * weight.x;
          else if (i == 3) result += morphTarget3 * weight.x;
        }

        return result;
      }

      void main() {
        vUv = uv;

        // 应用混合形状
        vec3 transformed = applyBlendShape(position);

        // 应用骨骼动画
        #include <skinning_vertex>

        // 计算法线
        vNormal = normalMatrix * normal;

        // 投影
        gl_Position = projectionMatrix * modelViewMatrix * vec4(transformed, 1.0);
      }
    `;
  }

  /**
   * 获取默认片段着色器
   * @returns 片段着色器代码
   */
  private getDefaultFragmentShader(): string {
    return `
      varying vec2 vUv;
      varying vec3 vNormal;

      void main() {
        vec3 normal = normalize(vNormal);
        vec3 light = normalize(vec3(1.0, 1.0, 1.0));
        float diffuse = max(0.0, dot(normal, light));

        gl_FragColor = vec4(vec3(diffuse), 1.0);
      }
    `;
  }

  /**
   * 设置混合形状权重
   * @param name 混合形状名称
   * @param weight 权重
   */
  public setBlendShapeWeight(name: string, weight: number): void {
    if (!this.initialized || !this.blendShapeData || !this.blendShapeTexture) return;

    // 获取混合形状索引
    const index = this.blendShapeMap.get(name);
    if (index === undefined) {
      // console.warn(`混合形状不存在: ${name}`);
      return;
    }

    // 计算纹理坐标
    const size = this.textureSize;
    const x = index % size;
    const y = Math.floor(index / size);
    const i = (y * size + x) * 4;

    // 设置权重
    this.blendShapeData[i] = weight;
    this.blendShapeData[i + 1] = 0;
    this.blendShapeData[i + 2] = 0;
    this.blendShapeData[i + 3] = 0;

    this.needsUpdate = true;
  }

  /**
   * 设置表情
   * @param expression 表情类型
   * @param weight 权重
   */
  public setExpression(expression: FacialExpressionType, weight: number): void {
    if (!this.initialized) return;

    // 获取表情对应的混合形状
    const blendShapeName = this.expressionMap.get(expression);
    if (!blendShapeName) {
      console.warn(`表情映射不存在: ${expression}`);
      return;
    }

    // 设置混合形状权重
    this.setBlendShapeWeight(blendShapeName, weight);
  }

  /**
   * 设置口型
   * @param viseme 口型类型
   * @param weight 权重
   */
  public setViseme(viseme: VisemeType, weight: number): void {
    if (!this.initialized) return;

    // 获取口型对应的混合形状
    const blendShapeName = this.visemeMap.get(viseme);
    if (!blendShapeName) {
      console.warn(`口型映射不存在: ${viseme}`);
      return;
    }

    // 设置混合形状权重
    this.setBlendShapeWeight(blendShapeName, weight);
  }

  /**
   * 设置表情映射
   * @param expression 表情类型
   * @param blendShapeName 混合形状名称
   */
  public setExpressionMapping(expression: FacialExpressionType, blendShapeName: string): void {
    this.expressionMap.set(expression, blendShapeName);
  }

  /**
   * 设置口型映射
   * @param viseme 口型类型
   * @param blendShapeName 混合形状名称
   */
  public setVisemeMapping(viseme: VisemeType, blendShapeName: string): void {
    this.visemeMap.set(viseme, blendShapeName);
  }

  /**
   * 设置LOD级别
   * @param level LOD级别
   */
  public setLODLevel(level: number): void {
    this.lodLevel = level;

    // 更新材质中的LOD级别
    for (const material of this.gpuMaterials.values()) {
      if (material instanceof THREE.ShaderMaterial) {
        material.uniforms.lodLevel.value = level;
      } else if (Array.isArray(material)) {
        for (const mat of material) {
          if (mat instanceof THREE.ShaderMaterial) {
            mat.uniforms.lodLevel.value = level;
          }
        }
      }
    }
  }

  /**
   * 恢复原始材质
   * @param mesh 骨骼网格
   */
  public restoreOriginalMaterial(mesh: THREE.Mesh): void {
    const originalMaterial = this.originalMaterials.get(mesh);
    if (originalMaterial) {
      mesh.material = originalMaterial;
    }
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized || !this.needsUpdate || !this.blendShapeTexture) return;

    // 更新纹理
    this.blendShapeTexture.needsUpdate = true;
    this.needsUpdate = false;
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.off(event, callback);
  }
}
