/**
 * 面部肌肉模拟
 * 使用物理引擎模拟面部肌肉运动
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { Entity } from '../../core/Entity';
import { Component } from '../../core/Component';
import { CannonPhysicsEngine, PhysicsObjectType, PhysicsConstraintType } from './CannonPhysicsEngine';
import { FacialExpressionType } from '../FacialExpressionType';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 肌肉类型
 */
export enum MuscleType {
  /** 眉毛 */
  EYEBROW = 'eyebrow',
  /** 眼睑 */
  EYELID = 'eyelid',
  /** 眼角 */
  EYE_CORNER = 'eye_corner',
  /** 鼻子 */
  NOSE = 'nose',
  /** 嘴唇 */
  LIP = 'lip',
  /** 嘴角 */
  MOUTH_CORNER = 'mouth_corner',
  /** 下巴 */
  JAW = 'jaw',
  /** 脸颊 */
  CHEEK = 'cheek'
}

/**
 * 肌肉配置
 */
export interface MuscleConfig {
  /** 肌肉类型 */
  type: MuscleType;
  /** 肌肉名称 */
  name: string;
  /** 起点 */
  start: THREE.Vector3;
  /** 终点 */
  end: THREE.Vector3;
  /** 质量 */
  mass?: number;
  /** 半径 */
  radius?: number;
  /** 弹性 */
  stiffness?: number;
  /** 阻尼 */
  damping?: number;
  /** 最大力 */
  maxForce?: number;
  /** 是否固定起点 */
  fixedStart?: boolean;
  /** 是否固定终点 */
  fixedEnd?: boolean;
}

/**
 * 面部肌肉模拟配置
 */
export interface FacialMuscleSimulationConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 物理引擎 */
  physicsEngine?: CannonPhysicsEngine;
  /** 重力 */
  gravity?: THREE.Vector3;
  /** 迭代次数 */
  iterations?: number;
  /** 是否使用软体 */
  useSoftBodies?: boolean;
}

/**
 * 面部肌肉模拟组件
 */
export class FacialMuscleSimulationComponent extends Component {
  /** 组件类型 */
  static readonly type = 'FacialMuscleSimulation';

  /** 物理引擎 */
  private physicsEngine: CannonPhysicsEngine;
  /** 肌肉配置 */
  private muscles: Map<string, MuscleConfig> = new Map();
  /** 肌肉物理对象 */
  private muscleBodies: Map<string, CANNON.Body> = new Map();
  /** 肌肉约束 */
  private muscleConstraints: Map<string, CANNON.Constraint> = new Map();
  /** 是否启用模拟 */
  private simulationEnabled: boolean = true;
  /** 是否已初始化 */
  private initialized: boolean = false;
  /** 是否使用软体 */
  private useSoftBodies: boolean = false;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 是否启用调试 */
  private debug: boolean;

  /**
   * 构造函数
   * @param physicsEngine 物理引擎
   * @param config 配置
   */
  constructor(physicsEngine: CannonPhysicsEngine, config: FacialMuscleSimulationConfig = {}) {
    super(FacialMuscleSimulationComponent.type);

    this.physicsEngine = physicsEngine;
    this.debug = config.debug || false;
    this.useSoftBodies = config.useSoftBodies || false;
  }

  /**
   * 添加肌肉
   * @param config 肌肉配置
   */
  public addMuscle(config: MuscleConfig): void {
    this.muscles.set(config.name, config);

    if (this.initialized) {
      this.createMusclePhysics(config);
    }
  }

  /**
   * 移除肌肉
   * @param name 肌肉名称
   * @returns 是否成功移除
   */
  public removeMuscle(name: string): boolean {
    const muscle = this.muscles.get(name);
    if (!muscle) return false;

    // 移除物理对象
    const entity = this.getEntity();
    const entityId = entity ? entity.id : 'unknown';
    this.physicsEngine.removeBody(`${entityId}_muscle_${name}_start`);
    this.physicsEngine.removeBody(`${entityId}_muscle_${name}_end`);
    this.physicsEngine.removeConstraint(`${entityId}_muscle_${name}`);

    // 移除肌肉
    this.muscles.delete(name);
    this.muscleBodies.delete(`${name}_start`);
    this.muscleBodies.delete(`${name}_end`);
    this.muscleConstraints.delete(name);

    return true;
  }

  /**
   * 获取肌肉
   * @param name 肌肉名称
   * @returns 肌肉配置
   */
  public getMuscle(name: string): MuscleConfig | null {
    return this.muscles.get(name) || null;
  }

  /**
   * 获取所有肌肉
   * @returns 肌肉配置数组
   */
  public getMuscles(): MuscleConfig[] {
    return Array.from(this.muscles.values());
  }

  /**
   * 初始化
   */
  public initialize(): void {
    if (this.initialized) return;

    // 创建所有肌肉的物理对象
    for (const muscle of this.muscles.values()) {
      this.createMusclePhysics(muscle);
    }

    this.initialized = true;

    if (this.debug) {
      const entity = this.getEntity();
      const entityId = entity ? entity.id : 'unknown';
      console.log(`面部肌肉模拟已初始化: ${entityId}`);
    }
  }

  /**
   * 创建肌肉物理对象
   * @param muscle 肌肉配置
   */
  private createMusclePhysics(muscle: MuscleConfig): void {
    const entity = this.getEntity();
    const entityId = entity ? entity.id : 'unknown';

    // 创建起点物理对象
    const startBody = this.physicsEngine.createBody(`${entityId}_muscle_${muscle.name}_start`, {
      type: PhysicsObjectType.RIGID_BODY,
      mass: muscle.fixedStart ? 0 : (muscle.mass || 0.1),
      position: new THREE.Vector3(muscle.start.x, muscle.start.y, muscle.start.z),
      shape: new CANNON.Sphere(muscle.radius || 0.01),
      linearDamping: muscle.damping || 0.5,
      angularDamping: muscle.damping || 0.5,
      userData: { muscle, part: 'start' }
    });

    // 创建终点物理对象
    const endBody = this.physicsEngine.createBody(`${entityId}_muscle_${muscle.name}_end`, {
      type: PhysicsObjectType.RIGID_BODY,
      mass: muscle.fixedEnd ? 0 : (muscle.mass || 0.1),
      position: new THREE.Vector3(muscle.end.x, muscle.end.y, muscle.end.z),
      shape: new CANNON.Sphere(muscle.radius || 0.01),
      linearDamping: muscle.damping || 0.5,
      angularDamping: muscle.damping || 0.5,
      userData: { muscle, part: 'end' }
    });

    // 创建肌肉约束
    const constraint = this.physicsEngine.createConstraint(`${entityId}_muscle_${muscle.name}`, {
      type: PhysicsConstraintType.SPRING,
      bodyA: startBody,
      bodyB: endBody,
      maxForce: muscle.maxForce || 100,
      userData: { muscle }
    });

    // 存储物理对象
    this.muscleBodies.set(`${muscle.name}_start`, startBody);
    this.muscleBodies.set(`${muscle.name}_end`, endBody);
    this.muscleConstraints.set(muscle.name, constraint);

    if (this.debug) {
      console.log(`创建肌肉物理对象: ${muscle.name}`);
    }
  }

  /**
   * 应用表情
   * @param expression 表情类型
   * @param weight 权重
   */
  public applyExpression(expression: FacialExpressionType, weight: number): void {
    if (!this.simulationEnabled || !this.initialized) return;

    // 根据表情类型应用不同的肌肉力
    switch (expression) {
      case FacialExpressionType.HAPPY:
        this.applyHappyExpression(weight);
        break;

      case FacialExpressionType.SAD:
        this.applySadExpression(weight);
        break;

      case FacialExpressionType.ANGRY:
        this.applyAngryExpression(weight);
        break;

      case FacialExpressionType.SURPRISED:
        this.applySurprisedExpression(weight);
        break;

      case FacialExpressionType.FEAR:
        this.applyFearfulExpression(weight);
        break;

      case FacialExpressionType.DISGUSTED:
      case FacialExpressionType.DISGUST:
        this.applyDisgustedExpression(weight);
        break;

      case FacialExpressionType.NEUTRAL:
      default:
        this.resetMuscles();
        break;
    }
  }

  /**
   * 应用开心表情
   * @param weight 权重
   */
  private applyHappyExpression(weight: number): void {
    // 嘴角上扬
    this.applyMuscleForce('mouth_corner_left', new CANNON.Vec3(0, weight * 5, 0));
    this.applyMuscleForce('mouth_corner_right', new CANNON.Vec3(0, weight * 5, 0));

    // 眼角微微上扬
    this.applyMuscleForce('eye_corner_left', new CANNON.Vec3(0, weight * 2, 0));
    this.applyMuscleForce('eye_corner_right', new CANNON.Vec3(0, weight * 2, 0));

    // 脸颊上扬
    this.applyMuscleForce('cheek_left', new CANNON.Vec3(0, weight * 3, 0));
    this.applyMuscleForce('cheek_right', new CANNON.Vec3(0, weight * 3, 0));
  }

  /**
   * 应用悲伤表情
   * @param weight 权重
   */
  private applySadExpression(weight: number): void {
    // 嘴角下垂
    this.applyMuscleForce('mouth_corner_left', new CANNON.Vec3(0, -weight * 5, 0));
    this.applyMuscleForce('mouth_corner_right', new CANNON.Vec3(0, -weight * 5, 0));

    // 眉毛内侧上扬
    this.applyMuscleForce('eyebrow_inner_left', new CANNON.Vec3(0, weight * 3, 0));
    this.applyMuscleForce('eyebrow_inner_right', new CANNON.Vec3(0, weight * 3, 0));
  }

  /**
   * 应用愤怒表情
   * @param weight 权重
   */
  private applyAngryExpression(weight: number): void {
    // 眉毛下垂
    this.applyMuscleForce('eyebrow_left', new CANNON.Vec3(0, -weight * 5, 0));
    this.applyMuscleForce('eyebrow_right', new CANNON.Vec3(0, -weight * 5, 0));

    // 眉毛内侧靠拢
    this.applyMuscleForce('eyebrow_inner_left', new CANNON.Vec3(weight * 2, -weight * 3, 0));
    this.applyMuscleForce('eyebrow_inner_right', new CANNON.Vec3(-weight * 2, -weight * 3, 0));

    // 嘴唇紧闭
    this.applyMuscleForce('lip_upper', new CANNON.Vec3(0, -weight * 2, 0));
    this.applyMuscleForce('lip_lower', new CANNON.Vec3(0, weight * 2, 0));
  }

  /**
   * 应用惊讶表情
   * @param weight 权重
   */
  private applySurprisedExpression(weight: number): void {
    // 眉毛上扬
    this.applyMuscleForce('eyebrow_left', new CANNON.Vec3(0, weight * 5, 0));
    this.applyMuscleForce('eyebrow_right', new CANNON.Vec3(0, weight * 5, 0));

    // 眼睛睁大
    this.applyMuscleForce('eyelid_upper_left', new CANNON.Vec3(0, weight * 3, 0));
    this.applyMuscleForce('eyelid_upper_right', new CANNON.Vec3(0, weight * 3, 0));

    // 嘴巴张开
    this.applyMuscleForce('lip_upper', new CANNON.Vec3(0, weight * 3, 0));
    this.applyMuscleForce('lip_lower', new CANNON.Vec3(0, -weight * 5, 0));

    // 下巴下垂
    this.applyMuscleForce('jaw', new CANNON.Vec3(0, -weight * 8, 0));
  }

  /**
   * 应用恐惧表情
   * @param weight 权重
   */
  private applyFearfulExpression(weight: number): void {
    // 眉毛上扬
    this.applyMuscleForce('eyebrow_left', new CANNON.Vec3(0, weight * 4, 0));
    this.applyMuscleForce('eyebrow_right', new CANNON.Vec3(0, weight * 4, 0));

    // 眉毛内侧靠拢
    this.applyMuscleForce('eyebrow_inner_left', new CANNON.Vec3(weight * 1, weight * 3, 0));
    this.applyMuscleForce('eyebrow_inner_right', new CANNON.Vec3(-weight * 1, weight * 3, 0));

    // 嘴巴微张
    this.applyMuscleForce('lip_upper', new CANNON.Vec3(0, weight * 1, 0));
    this.applyMuscleForce('lip_lower', new CANNON.Vec3(0, -weight * 2, 0));
  }

  /**
   * 应用厌恶表情
   * @param weight 权重
   */
  private applyDisgustedExpression(weight: number): void {
    // 鼻子皱起
    this.applyMuscleForce('nose', new CANNON.Vec3(0, weight * 3, 0));

    // 嘴角上扬（不对称）
    this.applyMuscleForce('mouth_corner_left', new CANNON.Vec3(0, weight * 4, 0));

    // 上唇上扬
    this.applyMuscleForce('lip_upper', new CANNON.Vec3(0, weight * 2, 0));
  }

  /**
   * 应用蔑视表情
   * @param weight 权重
   */
  private applyContemptExpression(weight: number): void {
    // 一侧嘴角上扬
    this.applyMuscleForce('mouth_corner_right', new CANNON.Vec3(0, weight * 5, 0));

    // 一侧眉毛上扬
    this.applyMuscleForce('eyebrow_right', new CANNON.Vec3(0, weight * 3, 0));
  }

  /**
   * 应用肌肉力
   * @param muscleName 肌肉名称
   * @param force 力
   */
  private applyMuscleForce(muscleName: string, force: CANNON.Vec3): void {
    const endBody = this.muscleBodies.get(`${muscleName}_end`);
    if (!endBody) return;

    // 应用力
    endBody.applyForce(force, endBody.position);

    if (this.debug) {
      console.log(`应用肌肉力: ${muscleName}`, force);
    }
  }

  /**
   * 重置肌肉
   */
  private resetMuscles(): void {
    // 重置所有肌肉的力和速度
    for (const [name, body] of this.muscleBodies.entries()) {
      body.velocity.set(0, 0, 0);
      body.angularVelocity.set(0, 0, 0);

      // 如果是终点，重置位置
      if (name.endsWith('_end')) {
        const muscleName = name.replace('_end', '');
        const muscle = this.muscles.get(muscleName);

        if (muscle) {
          body.position.set(muscle.end.x, muscle.end.y, muscle.end.z);
        }
      }
    }

    if (this.debug) {
      console.log('重置肌肉');
    }
  }

  /**
   * 启用组件
   */
  public enableSimulation(): void {
    this.simulationEnabled = true;
  }

  /**
   * 禁用组件
   */
  public disableSimulation(): void {
    this.simulationEnabled = false;
  }

  /**
   * 是否已启用
   * @returns 是否已启用
   */
  public isSimulationEnabled(): boolean {
    return this.simulationEnabled;
  }

  /**
   * 是否已初始化
   * @returns 是否已初始化
   */
  public isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 设置参数
   * @param name 参数名称
   * @param value 参数值
   */
  public setParameter(name: string, value: any): void {
    switch (name) {
      case 'simulationEnabled':
        this.simulationEnabled = Boolean(value);
        break;
      case 'debug':
        this.debug = Boolean(value);
        break;
      default:
        console.warn(`未知参数: ${name}`);
        break;
    }
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.simulationEnabled || !this.initialized) return;

    // 物理更新由物理引擎处理

    // 发送更新事件
    this.eventEmitter.emit('update', { deltaTime });
  }
}
