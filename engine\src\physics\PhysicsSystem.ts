/**
 * 物理系统
 * 基于cannon.js实现物理模拟
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import { PhysicsBody } from './PhysicsBody';
import { PhysicsCollider } from './PhysicsCollider';
import { Debug } from '../utils/Debug';
import { CollisionDetector } from './collision/CollisionDetector';
import { CollisionEvent, CollisionEventType } from './collision/CollisionEvent';
import { PhysicsConstraint } from './constraints/PhysicsConstraint';
import { SpringConstraint } from './constraints/SpringConstraint';
import { CharacterControllerComponent } from './components/CharacterControllerComponent';
import { PhysicsRaycastResult } from './PhysicsRaycastResult';
import { ContinuousCollisionDetection, CCDOptions } from './ccd/ContinuousCollisionDetection';
import { PhysicsDebugger } from './debug/PhysicsDebugger';
import { EnhancedPhysicsDebugger } from './debug/EnhancedPhysicsDebugger';

export interface PhysicsSystemOptions {
  /** 重力 */
  gravity?: { x: number; y: number; z: number };
  /** 物理更新频率（Hz） */
  updateFrequency?: number;
  /** 是否启用休眠 */
  allowSleep?: boolean;
  /** 是否启用连续碰撞检测 */
  enableCCD?: boolean;
  /** 连续碰撞检测选项 */
  ccdOptions?: CCDOptions;
  /** 是否显示调试信息 */
  debug?: boolean;
  /** 是否使用增强型调试器 */
  useEnhancedDebugger?: boolean;
  /** 调试器选项 */
  debuggerOptions?: any;
  /** 迭代次数 */
  iterations?: number;
}

export class PhysicsSystem extends System {
  /** 系统类型 */
  public static readonly type: string = 'PhysicsSystem';

  /** 物理世界 */
  private physicsWorld: CANNON.World;

  /** 物理更新频率（Hz） */
  private updateFrequency: number;

  /** 物理更新时间步长（秒） */
  private fixedTimeStep: number;

  /** 物理更新累积时间 */
  private accumulator: number = 0;

  /** 物理体映射 */
  private bodies: Map<string, PhysicsBody> = new Map();

  /** 碰撞器映射 */
  private colliders: Map<string, PhysicsCollider> = new Map();

  /** 约束映射 */
  private constraints: Map<string, PhysicsConstraint> = new Map();

  /** 角色控制器映射 */
  private characterControllers: Map<string, CharacterControllerComponent> = new Map();

  /** 连续碰撞检测 */
  private ccd: ContinuousCollisionDetection | null = null;

  /** 调试渲染器 */
  private debugRenderer: THREE.Scene | null = null;

  /** 是否启用调试 */
  private debug: boolean;

  /** 是否使用增强型调试器 */
  private useEnhancedDebugger: boolean;

  /** 物理调试器 */
  private physicsDebugger: PhysicsDebugger | EnhancedPhysicsDebugger | null = null;

  /** 调试网格映射 */
  private debugMeshes: Map<CANNON.Body, THREE.Object3D> = new Map();

  /** 碰撞检测器 */
  private collisionDetector: CollisionDetector;

  /**
   * 创建物理系统
   * @param options 物理系统选项
   */
  constructor(options: PhysicsSystemOptions = {}) {
    super(1); // 优先级1，在渲染系统之前更新

    // 创建物理世界
    this.physicsWorld = new CANNON.World();

    // 设置重力
    const gravity = options.gravity || { x: 0, y: -9.82, z: 0 };
    this.physicsWorld.gravity.set(gravity.x, gravity.y, gravity.z);

    // 设置更新频率
    this.updateFrequency = options.updateFrequency || 60;
    this.fixedTimeStep = 1 / this.updateFrequency;

    // 设置休眠
    this.physicsWorld.allowSleep = options.allowSleep !== undefined ? options.allowSleep : true;

    // 设置迭代次数（使用类型断言）
    (this.physicsWorld.solver as any).iterations = options.iterations || 10;

    // 设置连续碰撞检测
    if (options.enableCCD) {
      // 创建连续碰撞检测
      this.ccd = new ContinuousCollisionDetection(this.physicsWorld, options.ccdOptions);
    }

    // 设置调试
    this.debug = options.debug || false;
    this.useEnhancedDebugger = options.useEnhancedDebugger || false;
    if (this.debug) {
      this.debugRenderer = new THREE.Scene();

      // 创建物理调试器
      if (this.useEnhancedDebugger) {
        this.physicsDebugger = new EnhancedPhysicsDebugger(this, options.debuggerOptions);
      } else {
        this.physicsDebugger = new PhysicsDebugger(this, options.debuggerOptions);
      }

      // 初始化物理调试器
      this.physicsDebugger.initialize();
    }

    // 创建碰撞检测器
    this.collisionDetector = new CollisionDetector(this.physicsWorld);

    // 监听碰撞事件
    this.collisionDetector.on(CollisionEventType.BEGIN, this.handleCollisionEvent.bind(this));
    this.collisionDetector.on(CollisionEventType.STAY, this.handleCollisionEvent.bind(this));
    this.collisionDetector.on(CollisionEventType.END, this.handleCollisionEvent.bind(this));
    this.collisionDetector.on(CollisionEventType.TRIGGER_ENTER, this.handleCollisionEvent.bind(this));
    this.collisionDetector.on(CollisionEventType.TRIGGER_STAY, this.handleCollisionEvent.bind(this));
    this.collisionDetector.on(CollisionEventType.TRIGGER_EXIT, this.handleCollisionEvent.bind(this));
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    if (this.engine) {
      // 获取世界中的所有实体
      const world = this.engine.getWorld();
      if (world) {
        // 查找具有物理组件的实体
        const entities = world.getAllEntities();
        for (const entity of entities) {
          this.setupEntityPhysics(entity);
        }

        // 监听实体创建事件
        world.on('entityCreated', this.handleEntityCreated.bind(this));

        // 监听实体移除事件
        world.on('entityRemoved', this.handleEntityRemoved.bind(this));
      }
    }
  }

  /**
   * 处理实体创建事件
   * @param entity 创建的实体
   */
  private handleEntityCreated(entity: Entity): void {
    this.setupEntityPhysics(entity);
  }

  /**
   * 处理实体移除事件
   * @param entity 移除的实体
   */
  private handleEntityRemoved(entity: Entity): void {
    this.removeEntityPhysics(entity);
  }

  /**
   * 设置实体的物理属性
   * @param entity 实体
   */
  private setupEntityPhysics(entity: Entity): void {
    // 检查实体是否有物理体组件
    const physicsBody = entity.getComponent<PhysicsBody>(PhysicsBody.type);
    if (physicsBody) {
      // 初始化物理体
      physicsBody.initialize(this.physicsWorld);

      // 添加到映射
      this.bodies.set(entity.id, physicsBody);

      // 注册到碰撞检测器
      this.collisionDetector.registerBody(entity, physicsBody);
    }

    // 检查实体是否有碰撞器组件
    const physicsCollider = entity.getComponent<PhysicsCollider>(PhysicsCollider.type);
    if (physicsCollider) {
      // 初始化碰撞器
      physicsCollider.initialize(this.physicsWorld);

      // 添加到映射
      this.colliders.set(entity.id, physicsCollider);

      // 注册到碰撞检测器
      this.collisionDetector.registerCollider(entity, physicsCollider);
    }

    // 检查实体是否有约束组件
    const constraint = entity.getComponent<PhysicsConstraint>(PhysicsConstraint.type);
    if (constraint) {
      // 初始化约束
      constraint.initialize(this.physicsWorld);

      // 添加到映射
      this.constraints.set(`${entity.id}_${constraint.constructor.name}`, constraint);
    }

    // 检查实体是否有角色控制器组件
    const characterController = entity.getComponent<CharacterControllerComponent>(CharacterControllerComponent.type);
    if (characterController) {
      // 初始化角色控制器
      characterController.initialize(this.physicsWorld);

      // 添加到映射
      this.characterControllers.set(entity.id, characterController);
    }
  }

  /**
   * 移除实体的物理属性
   * @param entity 实体
   */
  private removeEntityPhysics(entity: Entity): void {
    // 移除物理体
    const physicsBody = this.bodies.get(entity.id);
    if (physicsBody) {
      // 从碰撞检测器中注销
      this.collisionDetector.unregisterBody(entity);

      // 销毁物理体
      physicsBody.dispose();
      this.bodies.delete(entity.id);

      // 移除调试网格
      if (this.debug && this.debugRenderer) {
        const body = physicsBody.getCannonBody();
        if (body) {
          const debugMesh = this.debugMeshes.get(body);
          if (debugMesh) {
            this.debugRenderer.remove(debugMesh);
            this.debugMeshes.delete(body);
          }
        }
      }
    }

    // 移除碰撞器
    const physicsCollider = this.colliders.get(entity.id);
    if (physicsCollider) {
      // 从碰撞检测器中注销
      this.collisionDetector.unregisterCollider(entity);

      // 销毁碰撞器
      physicsCollider.dispose();
      this.colliders.delete(entity.id);
    }

    // 移除约束
    for (const [key, constraint] of this.constraints.entries()) {
      if (key.startsWith(`${entity.id}_`)) {
        constraint.dispose();
        this.constraints.delete(key);
      }
    }

    // 移除角色控制器
    const characterController = this.characterControllers.get(entity.id);
    if (characterController) {
      characterController.dispose();
      this.characterControllers.delete(entity.id);
    }
  }

  /**
   * 处理碰撞事件
   * @param event 碰撞事件
   */
  private handleCollisionEvent(event: CollisionEvent): void {
    // 发出碰撞事件
    this.emit(event.type, event);

    // 获取实体
    const entityA = event.entityA;
    const entityB = event.entityB;

    // 通知实体组件
    const physicsBodyA = this.bodies.get(entityA.id);
    const physicsBodyB = this.bodies.get(entityB.id);

    if (physicsBodyA) {
      switch (event.type) {
        case CollisionEventType.BEGIN:
          physicsBodyA.onCollisionStart(entityB, event);
          break;
        case CollisionEventType.STAY:
          physicsBodyA.onCollisionStay(entityB, event);
          break;
        case CollisionEventType.END:
          physicsBodyA.onCollisionEnd(entityB, event);
          break;
        case CollisionEventType.TRIGGER_ENTER:
          physicsBodyA.onTriggerEnter(entityB, event);
          break;
        case CollisionEventType.TRIGGER_STAY:
          physicsBodyA.onTriggerStay(entityB, event);
          break;
        case CollisionEventType.TRIGGER_EXIT:
          physicsBodyA.onTriggerExit(entityB, event);
          break;
      }
    }

    if (physicsBodyB) {
      switch (event.type) {
        case CollisionEventType.BEGIN:
          physicsBodyB.onCollisionStart(entityA, event);
          break;
        case CollisionEventType.STAY:
          physicsBodyB.onCollisionStay(entityA, event);
          break;
        case CollisionEventType.END:
          physicsBodyB.onCollisionEnd(entityA, event);
          break;
        case CollisionEventType.TRIGGER_ENTER:
          physicsBodyB.onTriggerEnter(entityA, event);
          break;
        case CollisionEventType.TRIGGER_STAY:
          physicsBodyB.onTriggerStay(entityA, event);
          break;
        case CollisionEventType.TRIGGER_EXIT:
          physicsBodyB.onTriggerExit(entityA, event);
          break;
      }
    }

    // 通知实体
    if (typeof entityA['onCollision'] === 'function') {
      entityA['onCollision'](event);
    }

    if (typeof entityB['onCollision'] === 'function') {
      entityB['onCollision'](event);
    }
  }

  /**
   * 固定时间步长更新
   * @param _fixedDeltaTime 固定帧间隔时间（秒）- 未使用，使用 this.fixedTimeStep 代替
   */
  public fixedUpdate(_fixedDeltaTime: number): void {
    // 更新弹簧约束
    for (const constraint of this.constraints.values()) {
      if (constraint instanceof SpringConstraint) {
        constraint.update();
      }
    }

    // 更新物理世界
    if (this.ccd) {
      // 使用连续碰撞检测更新
      this.ccd.update(this.fixedTimeStep);
    } else {
      // 正常更新
      this.physicsWorld.step(this.fixedTimeStep);
    }

    // 更新碰撞检测器
    this.collisionDetector.update(this.fixedTimeStep);

    // 更新实体位置和旋转
    for (const [_entityId, physicsBody] of this.bodies.entries()) {
      physicsBody.updateTransform();
    }

    // 更新调试渲染
    if (this.debug && this.debugRenderer) {
      if (this.physicsDebugger) {
        this.physicsDebugger.update();
      } else {
        this.updateDebugRenderer();
      }
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 累积时间
    this.accumulator += deltaTime;

    // 固定时间步长更新
    while (this.accumulator >= this.fixedTimeStep) {
      this.fixedUpdate(this.fixedTimeStep);
      this.accumulator -= this.fixedTimeStep;
    }
  }

  /**
   * 更新调试渲染器
   */
  private updateDebugRenderer(): void {
    if (!this.debugRenderer) return;

    // 更新现有调试网格
    for (const body of this.physicsWorld.bodies) {
      let mesh = this.debugMeshes.get(body);

      // 如果没有调试网格，创建一个
      if (!mesh) {
        mesh = this.createDebugMesh(body);
        if (mesh) {
          this.debugRenderer.add(mesh);
          this.debugMeshes.set(body, mesh);
        }
      }

      // 更新位置和旋转
      if (mesh) {
        mesh.position.copy(new THREE.Vector3(
          body.position.x,
          body.position.y,
          body.position.z
        ));

        mesh.quaternion.copy(new THREE.Quaternion(
          body.quaternion.x,
          body.quaternion.y,
          body.quaternion.z,
          body.quaternion.w
        ));
      }
    }
  }

  /**
   * 创建调试网格
   * @param body 物理体
   * @returns 调试网格
   */
  private createDebugMesh(body: CANNON.Body): THREE.Object3D {
    const group = new THREE.Group();

    // 为每个形状创建网格
    for (let i = 0; i < body.shapes.length; i++) {
      const shape = body.shapes[i];
      const material = new THREE.MeshBasicMaterial({
        color: 0x00ff00,
        wireframe: true,
        transparent: true,
        opacity: 0.5,
      });

      let geometry: THREE.BufferGeometry;
      let mesh: THREE.Mesh;

      // 根据形状类型创建几何体
      switch (shape.type) {
        case CANNON.Shape.types.BOX:
          const boxShape = shape as CANNON.Box;
          geometry = new THREE.BoxGeometry(
            boxShape.halfExtents.x * 2,
            boxShape.halfExtents.y * 2,
            boxShape.halfExtents.z * 2
          );
          mesh = new THREE.Mesh(geometry, material);
          break;

        case CANNON.Shape.types.SPHERE:
          const sphereShape = shape as CANNON.Sphere;
          geometry = new THREE.SphereGeometry(sphereShape.radius, 16, 16);
          mesh = new THREE.Mesh(geometry, material);
          break;

        case CANNON.Shape.types.CYLINDER:
          const cylinderShape = shape as any; // CANNON.Cylinder
          geometry = new THREE.CylinderGeometry(
            cylinderShape.radiusTop,
            cylinderShape.radiusBottom,
            cylinderShape.height,
            16
          );
          mesh = new THREE.Mesh(geometry, material);
          // 旋转以匹配CANNON.js的方向
          mesh.rotation.x = Math.PI / 2;
          break;

        case CANNON.Shape.types.PLANE:
          geometry = new THREE.PlaneGeometry(10, 10);
          mesh = new THREE.Mesh(geometry, material);
          break;

        default:
          // 对于不支持的形状，创建一个小球作为占位符
          geometry = new THREE.SphereGeometry(0.1, 8, 8);
          mesh = new THREE.Mesh(geometry, material);
          Debug.warn(`不支持的物理形状类型: ${shape.type}`);
          break;
      }

      // 应用形状的位置和旋转
      if (body.shapeOffsets[i]) {
        mesh.position.copy(new THREE.Vector3(
          body.shapeOffsets[i].x,
          body.shapeOffsets[i].y,
          body.shapeOffsets[i].z
        ));
      }

      if (body.shapeOrientations[i]) {
        mesh.quaternion.copy(new THREE.Quaternion(
          body.shapeOrientations[i].x,
          body.shapeOrientations[i].y,
          body.shapeOrientations[i].z,
          body.shapeOrientations[i].w
        ));
      }

      group.add(mesh);
    }

    return group;
  }

  /**
   * 获取调试渲染器场景
   * @returns 调试渲染器场景
   */
  public getDebugRenderer(): THREE.Scene | null {
    return this.debugRenderer;
  }

  /**
   * 设置重力
   * @param x X轴重力
   * @param y Y轴重力
   * @param z Z轴重力
   */
  public setGravity(x: number, y: number, z: number): void {
    this.physicsWorld.gravity.set(x, y, z);
  }

  /**
   * 获取重力
   * @returns 重力向量
   */
  public getGravity(): CANNON.Vec3 {
    return this.physicsWorld.gravity;
  }

  /**
   * 射线检测（最近的一个）
   * @param from 起点
   * @param to 终点
   * @param options 选项
   * @returns 射线检测结果
   */
  public raycastClosest(
    from: THREE.Vector3 | { x: number; y: number; z: number },
    to: THREE.Vector3 | { x: number; y: number; z: number },
    options: {
      skipBackfaces?: boolean;
      collisionFilterMask?: number;
      collisionFilterGroup?: number;
    } = {}
  ): PhysicsRaycastResult {
    // 创建射线
    const rayFrom = new CANNON.Vec3(from.x, from.y, from.z);
    const rayTo = new CANNON.Vec3(to.x, to.y, to.z);

    // 设置选项
    const rayOptions: CANNON.RayOptions = {
      skipBackfaces: options.skipBackfaces !== undefined ? options.skipBackfaces : true,
      collisionFilterMask: options.collisionFilterMask !== undefined ? options.collisionFilterMask : -1,
      collisionFilterGroup: options.collisionFilterGroup !== undefined ? options.collisionFilterGroup : -1,
    };

    // 创建射线检测结果
    const result = new CANNON.RaycastResult();

    // 执行射线检测
    this.physicsWorld.raycastClosest(rayFrom, rayTo, rayOptions, result);

    // 创建物理体到实体的映射
    const bodyToEntity = new Map<CANNON.Body, Entity>();
    for (const [entityId, physicsBody] of this.bodies.entries()) {
      const body = physicsBody.getCannonBody();
      if (body) {
        const entity = this.engine?.getWorld().getEntity(entityId);
        if (entity) {
          bodyToEntity.set(body, entity);
        }
      }
    }

    // 返回结果
    return new PhysicsRaycastResult(result, bodyToEntity);
  }

  /**
   * 射线检测（所有相交的）
   * @param from 起点
   * @param to 终点
   * @param options 选项
   * @returns 射线检测结果数组
   */
  public raycastAll(
    from: THREE.Vector3 | { x: number; y: number; z: number },
    to: THREE.Vector3 | { x: number; y: number; z: number },
    options: {
      skipBackfaces?: boolean;
      collisionFilterMask?: number;
      collisionFilterGroup?: number;
    } = {}
  ): PhysicsRaycastResult[] {
    // 创建射线
    const rayFrom = new CANNON.Vec3(from.x, from.y, from.z);
    const rayTo = new CANNON.Vec3(to.x, to.y, to.z);

    // 设置选项
    const rayOptions: CANNON.RayOptions = {
      skipBackfaces: options.skipBackfaces !== undefined ? options.skipBackfaces : true,
      collisionFilterMask: options.collisionFilterMask !== undefined ? options.collisionFilterMask : -1,
      collisionFilterGroup: options.collisionFilterGroup !== undefined ? options.collisionFilterGroup : -1,
    };

    // 创建物理体到实体的映射
    const bodyToEntity = new Map<CANNON.Body, Entity>();
    for (const [entityId, physicsBody] of this.bodies.entries()) {
      const body = physicsBody.getCannonBody();
      if (body) {
        const entity = this.engine?.getWorld().getEntity(entityId);
        if (entity) {
          bodyToEntity.set(body, entity);
        }
      }
    }

    // 执行射线检测
    const results: PhysicsRaycastResult[] = [];
    this.physicsWorld.raycastAll(rayFrom, rayTo, rayOptions, (result) => {
      results.push(new PhysicsRaycastResult(result, bodyToEntity));
    });

    // 返回结果
    return results;
  }

  /**
   * 射线检测（任意一个）
   * @param from 起点
   * @param to 终点
   * @param options 选项
   * @returns 射线检测结果
   */
  public raycast(
    from: THREE.Vector3 | { x: number; y: number; z: number },
    to: THREE.Vector3 | { x: number; y: number; z: number },
    options: {
      skipBackfaces?: boolean;
      collisionFilterMask?: number;
      collisionFilterGroup?: number;
    } = {}
  ): PhysicsRaycastResult {
    // 创建射线
    const rayFrom = new CANNON.Vec3(from.x, from.y, from.z);
    const rayTo = new CANNON.Vec3(to.x, to.y, to.z);

    // 设置选项
    const rayOptions: CANNON.RayOptions = {
      skipBackfaces: options.skipBackfaces !== undefined ? options.skipBackfaces : true,
      collisionFilterMask: options.collisionFilterMask !== undefined ? options.collisionFilterMask : -1,
      collisionFilterGroup: options.collisionFilterGroup !== undefined ? options.collisionFilterGroup : -1,
    };

    // 创建射线检测结果
    const result = new CANNON.RaycastResult();

    // 创建物理体到实体的映射
    const bodyToEntity = new Map<CANNON.Body, Entity>();
    for (const [entityId, physicsBody] of this.bodies.entries()) {
      const body = physicsBody.getCannonBody();
      if (body) {
        const entity = this.engine?.getWorld().getEntity(entityId);
        if (entity) {
          bodyToEntity.set(body, entity);
        }
      }
    }

    // 执行射线检测
    this.physicsWorld.raycastAny(rayFrom, rayTo, rayOptions, result);

    // 返回结果
    return new PhysicsRaycastResult(result, bodyToEntity);
  }

  /**
   * 获取物理世界
   * @returns 物理世界
   */
  public getPhysicsWorld(): CANNON.World {
    return this.physicsWorld;
  }

  /**
   * 获取实体的物理体
   * @param entity 实体
   * @returns 物理体
   */
  public getPhysicsBody(entity: Entity): PhysicsBody | null {
    return this.bodies.get(entity.id) || null;
  }

  /**
   * 设置调试模式
   * @param debug 是否启用调试
   * @param useEnhancedDebugger 是否使用增强型调试器
   * @param debuggerOptions 调试器选项
   */
  public setDebug(debug: boolean, useEnhancedDebugger: boolean = false, debuggerOptions: any = {}): void {
    if (this.debug === debug && this.useEnhancedDebugger === useEnhancedDebugger) {
      return;
    }

    this.debug = debug;
    this.useEnhancedDebugger = useEnhancedDebugger;

    if (debug && !this.debugRenderer) {
      this.debugRenderer = new THREE.Scene();

      // 创建物理调试器
      if (useEnhancedDebugger) {
        this.physicsDebugger = new EnhancedPhysicsDebugger(this, debuggerOptions);
      } else {
        this.physicsDebugger = new PhysicsDebugger(this, debuggerOptions);
      }

      // 初始化物理调试器
      this.physicsDebugger.initialize();
    } else if (!debug && this.debugRenderer) {
      // 清空调试渲染器
      if (this.physicsDebugger) {
        if (typeof (this.physicsDebugger as any).dispose === 'function') {
          (this.physicsDebugger as any).dispose();
        }
        this.physicsDebugger = null;
      } else {
        while (this.debugRenderer.children.length > 0) {
          const child = this.debugRenderer.children[0];
          this.debugRenderer.remove(child);
        }
        this.debugMeshes.clear();
      }

      this.debugRenderer = null;
    }
  }

  /**
   * 是否启用调试
   * @returns 是否启用调试
   */
  public isDebug(): boolean {
    return this.debug;
  }

  /**
   * 是否使用增强型调试器
   * @returns 是否使用增强型调试器
   */
  public isUsingEnhancedDebugger(): boolean {
    return this.useEnhancedDebugger;
  }

  /**
   * 获取物理调试器
   * @returns 物理调试器
   */
  public getPhysicsDebugger(): PhysicsDebugger | EnhancedPhysicsDebugger | null {
    return this.physicsDebugger;
  }

  /**
   * 计算角色控制器移动
   * @param entity 实体
   * @param desiredTranslation 期望的移动向量
   * @param filterGroups 碰撞组过滤
   * @param filterPredicate 碰撞过滤谓词函数
   */
  public computeCharacterControllerMovement(
    entity: Entity,
    desiredTranslation: THREE.Vector3,
    filterGroups?: number,
    filterPredicate?: (body: CANNON.Body) => boolean
  ): void {
    const characterController = this.characterControllers.get(entity.id);
    if (!characterController) {
      console.warn(`实体 ${entity.id} 没有角色控制器组件`);
      return;
    }

    characterController.computeColliderMovement(desiredTranslation, filterGroups, filterPredicate);
  }

  /**
   * 获取角色控制器计算出的移动向量
   * @param entity 实体
   * @returns 计算出的移动向量
   */
  public getCharacterControllerComputedMovement(entity: Entity): THREE.Vector3 {
    const characterController = this.characterControllers.get(entity.id);
    if (!characterController) {
      console.warn(`实体 ${entity.id} 没有角色控制器组件`);
      return new THREE.Vector3();
    }

    return characterController.getComputedMovement();
  }

  /**
   * 检查实体是否有角色控制器
   * @param entity 实体
   * @returns 是否有角色控制器
   */
  public hasCharacterController(entity: Entity): boolean {
    return this.characterControllers.has(entity.id);
  }

  /**
   * 获取角色控制器
   * @param entity 实体
   * @returns 角色控制器
   */
  public getCharacterController(entity: Entity): CharacterControllerComponent | null {
    return this.characterControllers.get(entity.id) || null;
  }

  /**
   * 启用实体的连续碰撞检测
   * @param entity 实体
   */
  public enableEntityCCD(entity: Entity): void {
    if (!this.ccd) {
      console.warn('连续碰撞检测未启用');
      return;
    }

    this.ccd.enableEntityCCD(entity);
  }

  /**
   * 禁用实体的连续碰撞检测
   * @param entity 实体
   */
  public disableEntityCCD(entity: Entity): void {
    if (!this.ccd) {
      return;
    }

    this.ccd.disableEntityCCD(entity);
  }

  /**
   * 检查实体是否启用连续碰撞检测
   * @param entity 实体
   * @returns 是否启用连续碰撞检测
   */
  public isEntityCCDEnabled(entity: Entity): boolean {
    if (!this.ccd) {
      return false;
    }

    return this.ccd.isEntityCCDEnabled(entity);
  }

  /**
   * 获取连续碰撞检测
   * @returns 连续碰撞检测
   */
  public getCCD(): ContinuousCollisionDetection | null {
    return this.ccd;
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 销毁碰撞检测器
    this.collisionDetector.dispose();

    // 移除实体事件监听
    if (this.engine) {
      const world = this.engine.getWorld();
      if (world) {
        world.off('entityCreated', this.handleEntityCreated.bind(this));
        world.off('entityRemoved', this.handleEntityRemoved.bind(this));
      }
    }

    // 清空物理体和碰撞器
    for (const physicsBody of this.bodies.values()) {
      physicsBody.dispose();
    }
    this.bodies.clear();

    for (const physicsCollider of this.colliders.values()) {
      physicsCollider.dispose();
    }
    this.colliders.clear();

    // 清空约束
    for (const constraint of this.constraints.values()) {
      constraint.dispose();
    }
    this.constraints.clear();

    // 清空角色控制器
    for (const characterController of this.characterControllers.values()) {
      characterController.dispose();
    }
    this.characterControllers.clear();

    // 清空调试渲染器
    if (this.debugRenderer) {
      while (this.debugRenderer.children.length > 0) {
        const child = this.debugRenderer.children[0];
        this.debugRenderer.remove(child);
      }
      this.debugMeshes.clear();
      this.debugRenderer = null;
    }

    super.dispose();
  }
}
